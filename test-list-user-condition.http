# 测试 List<UserCondition> 查询功能

### 1. 测试新的 List<UserCondition> 结构（每个条件查询一条数据）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}

###

### 2. 调试新的 List<UserCondition> 结构
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}

###

### 3. 测试单个 UserCondition（只有抖音ID）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}

###

### 4. 测试单个 UserCondition（只有星图ID）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": [
    {
      "starMapId": "6857515126107406344"
    }
  ]
}

###

### 5. 测试单个 UserCondition（只有用户ID）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": [
    {
      "userId": "68370542454"
    }
  ]
}

###

### 6. 测试单个 UserCondition（只有抖音链接）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": [
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}

###

### 7. 测试多个 UserCondition（每个只查询一条数据）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "douyinId": "test_user_002"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "starMapId": "star_test_002"
    },
    {
      "userId": "user_test_001"
    }
  ]
}

###

### 8. 测试向后兼容（使用原有的字段）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userIds": "68370542454,test_user_002",
  "douyinIds": ["douyin_test_001"],
  "starMapIds": ["6857515126107406344"]
}

###

### 9. 测试空的 userCondition 数组
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": []
}

###

### 10. 测试无效的 UserCondition（空对象）
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": [
    {},
    {
      "douyinId": ""
    },
    {
      "douyinId": "68370542454"
    }
  ]
}
