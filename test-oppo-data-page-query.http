# OPPO数据分页查询接口测试

### 1. 基础分页查询（查询当前周表的所有数据）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10
}

###

### 2. 根据批次ID查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "batchId": "EXCEL_20250106153000_001",
  "pageNum": 1,
  "pageSize": 20
}

###

### 3. 根据用户ID列表查询（支持抖音ID和星图ID混合）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userIds": ["user123", "user456", "user789"],
  "pageNum": 1,
  "pageSize": 10
}

###

### 4. 根据抖音ID列表查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "douyinIds": ["douyin123", "douyin456"],
  "pageNum": 1,
  "pageSize": 15
}

###

### 5. 根据星图ID列表查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "starMapIds": ["starmap123", "starmap456"],
  "pageNum": 1,
  "pageSize": 10
}

###

### 6. 根据时间范围查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "startUploadTime": "2025-01-06 00:00:00",
  "endUploadTime": "2025-01-06 23:59:59",
  "pageNum": 1,
  "pageSize": 50
}

###

### 7. 指定周表查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "weekTableName": "oppo_data_2025_27",
  "pageNum": 1,
  "pageSize": 10
}

###

### 8. 复合条件查询（批次ID + 用户ID + 时间范围）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "batchId": "EXCEL_20250106153000_001",
  "douyinIds": ["douyin123", "douyin456"],
  "startUploadTime": "2025-01-06 15:00:00",
  "endUploadTime": "2025-01-06 18:00:00",
  "weekTableName": "oppo_data_2025_27",
  "pageNum": 1,
  "pageSize": 25
}

###

### 9. 自定义排序查询（按粉丝数降序）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "orderBy": "followers_count",
  "orderDirection": "DESC",
  "pageNum": 1,
  "pageSize": 10
}

###

### 10. 大页面查询（测试分页性能）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 100
}

###

### 11. 获取可用的周表列表
GET http://localhost:10000/api/oppo/available-tables

###

### 12. 查询第2页数据
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 2,
  "pageSize": 20
}

###

### 13. 按创建时间升序查询
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "orderBy": "create_time",
  "orderDirection": "ASC",
  "pageNum": 1,
  "pageSize": 15
}

###

### 14. 混合ID类型查询（同时使用douyinIds和starMapIds）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "douyinIds": ["douyin123", "douyin456"],
  "starMapIds": ["starmap789", "starmap101"],
  "pageNum": 1,
  "pageSize": 10
}

###

### 15. 空条件查询（测试默认行为）
POST http://localhost:10000/api/oppo/page-query
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q"
}
