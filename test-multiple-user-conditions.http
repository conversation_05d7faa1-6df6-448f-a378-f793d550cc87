# 测试多个 UserCondition 查询功能

### 1. 查询单个用户（一个 UserCondition）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}

###

### 2. 查询多个用户（不同字段类型）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "userId": "user_123456"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}

###

### 3. 查询多个用户（相同字段类型 - 多个抖音ID）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "douyinId": "68370542455"
    },
    {
      "douyinId": "68370542456"
    },
    {
      "douyinId": "test_user_001"
    },
    {
      "douyinId": "test_user_002"
    }
  ]
}

###

### 4. 查询多个用户（相同字段类型 - 多个星图ID）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "starMapId": "6857515126107406344"
    },
    {
      "starMapId": "6857515126107406345"
    },
    {
      "starMapId": "star_test_001"
    },
    {
      "starMapId": "star_test_002"
    }
  ]
}

###

### 5. 批量查询（混合多种字段类型）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 20,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "douyinId": "68370542455"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "starMapId": "6857515126107406345"
    },
    {
      "userId": "user_123456"
    },
    {
      "userId": "user_789012"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_002"
    },
    {
      "douyinId": "test_user_003"
    },
    {
      "starMapId": "star_test_003"
    }
  ]
}

###

### 6. 调试多个 UserCondition
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "userId": "user_123456"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}

###

### 7. 大批量查询测试（10个用户）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 50,
  "userCondition": [
    {"douyinId": "user_001"},
    {"douyinId": "user_002"},
    {"douyinId": "user_003"},
    {"starMapId": "star_001"},
    {"starMapId": "star_002"},
    {"starMapId": "star_003"},
    {"userId": "internal_001"},
    {"userId": "internal_002"},
    {"douyinLink": "https://www.douyin.com/user/link_001"},
    {"douyinLink": "https://www.douyin.com/user/link_002"}
  ]
}

###

### 8. 结合其他查询条件（批次ID + 多个用户）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "batchId": "EXCEL_20250106153000_001",
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    }
  ]
}

###

### 9. 结合时间范围 + 多个用户
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "startUploadTime": "2025-01-01 00:00:00",
  "endUploadTime": "2025-01-31 23:59:59",
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "userId": "user_123456"
    }
  ]
}

###

### 10. 空的 userCondition 数组测试
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": []
}
