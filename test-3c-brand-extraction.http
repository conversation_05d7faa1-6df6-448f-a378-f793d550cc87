# 测试3C品牌提取功能

### 1. 测试包含单个3C品牌的数据
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5
}

###

### 2. 查看返回的数据中是否包含 threeCCommercialCooperationBrands 字段
GET http://localhost:10000/api/oppo/latest-table

###

### 3. 测试特定用户的3C品牌提取
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}

###

### 4. 测试多个用户的3C品牌提取
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    }
  ]
}

###

### 5. 查看系统诊断信息
GET http://localhost:10000/api/oppo/diagnose

###

### 6. 测试健康检查
GET http://localhost:10000/api/oppo/health
