# 用户查询条件返回 0 条记录问题修复

## 🚨 问题描述

用户使用以下查询条件时返回 0 条记录：
```json
"userCondition": {
    "douyinIds": ["68370542454", "test_user_002"],
    "starMapIds": ["6857515126107406344", "star_test_002"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
}
```

## 🔍 问题根本原因

### 原有的 SQL 逻辑问题
原来的 SQL 查询使用了 **AND** 逻辑连接所有条件：

```sql
WHERE douyin_id IN ('68370542454', 'test_user_002')
  AND star_map_id IN ('6857515126107406344', 'star_test_002')
  AND douyin_link IN ('https://www.douyin.com/user/test_user_001')
```

这要求一条记录必须**同时满足**所有这些条件：
- `douyin_id` 必须是 '68370542454' 或 'test_user_002'
- **同时** `star_map_id` 必须是 '6857515126107406344' 或 'star_test_002'
- **同时** `douyin_link` 必须是 'https://www.douyin.com/user/test_user_001'

但实际上，一条记录不可能同时满足多个不同的 ID 条件，因为：
- 一条记录只有一个 `douyin_id`
- 一条记录只有一个 `star_map_id`
- 一条记录只有一个 `douyin_link`

## ✅ 修复方案

### 1. 修改 SQL 逻辑为 OR 连接

将查询逻辑改为 **OR** 连接，这样只要满足任意一个条件就能查询到结果：

```sql
WHERE (
    douyin_id IN ('68370542454', 'test_user_002')
    OR star_map_id IN ('6857515126107406344', 'star_test_002')
    OR douyin_link IN ('https://www.douyin.com/user/test_user_001')
)
```

### 2. 修复后的 XML 实现

```xml
<!-- 处理用户查询条件（新的统一方式） -->
<if test="request.userCondition != null">
    <choose>
        <when test="(request.userCondition.userIds != null and request.userCondition.userIds != '') or 
                   (request.userCondition.userIdList != null and request.userCondition.userIdList.size() > 0) or
                   (request.userCondition.douyinIds != null and request.userCondition.douyinIds.size() > 0) or
                   (request.userCondition.starMapIds != null and request.userCondition.starMapIds.size() > 0) or
                   (request.userCondition.douyinLinks != null and request.userCondition.douyinLinks.size() > 0)">
            AND (
            <trim suffixOverrides="OR">
                <!-- 各种ID条件用 OR 连接 -->
                <if test="request.userCondition.douyinIds != null and request.userCondition.douyinIds.size() > 0">
                    douyin_id IN
                    <foreach collection="request.userCondition.douyinIds" item="douyinId" open="(" separator="," close=")">
                        #{douyinId}
                    </foreach>
                    OR
                </if>
                
                <if test="request.userCondition.starMapIds != null and request.userCondition.starMapIds.size() > 0">
                    star_map_id IN
                    <foreach collection="request.userCondition.starMapIds" item="starMapId" open="(" separator="," close=")">
                        #{starMapId}
                    </foreach>
                    OR
                </if>
                
                <if test="request.userCondition.douyinLinks != null and request.userCondition.douyinLinks.size() > 0">
                    douyin_link IN
                    <foreach collection="request.userCondition.douyinLinks" item="douyinLink" open="(" separator="," close=")">
                        #{douyinLink}
                    </foreach>
                    OR
                </if>
            </trim>
            )
        </when>
    </choose>
</if>
```

## 🧪 调试工具

### 新增调试接口
```http
POST /api/oppo/debug-user-condition
```

这个接口会返回：
1. **有效的用户查询条件**：解析后的查询条件
2. **查询分析**：条件是否有效、包含哪些ID
3. **实际查询结果数量**：执行查询后的记录数
4. **数据存在性检查**：检查数据库中是否存在指定的ID
5. **示例数据**：如果有结果，返回前几条数据

### 调试响应示例
```json
{
  "success": true,
  "data": {
    "effectiveUserCondition": {
      "douyinIds": ["68370542454", "test_user_002"],
      "starMapIds": ["6857515126107406344", "star_test_002"],
      "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
    },
    "analysis": {
      "hasAnyUserCondition": true,
      "hasDouyinLinkCondition": true,
      "allUserIds": ["68370542454", "test_user_002", "6857515126107406344", "star_test_002"],
      "allDouyinIds": ["68370542454", "test_user_002"],
      "allStarMapIds": ["6857515126107406344", "star_test_002"],
      "allDouyinLinks": ["https://www.douyin.com/user/test_user_001"]
    },
    "tableName": "oppo_data_2025_01",
    "actualCount": 2,
    "dataCheck": {
      "douyinId_68370542454": "存在",
      "douyinId_test_user_002": "不存在",
      "starMapId_6857515126107406344": "存在",
      "starMapId_star_test_002": "不存在"
    },
    "sampleData": [
      {
        "userId": "68370542454",
        "douyinId": "68370542454",
        "starMapId": null,
        "userNickname": "测试用户1"
      },
      {
        "userId": "6857515126107406344",
        "douyinId": null,
        "starMapId": "6857515126107406344",
        "userNickname": "测试用户2"
      }
    ]
  }
}
```

## 🔧 排查步骤

### 1. 使用调试接口检查
```http
POST /api/oppo/debug-user-condition
{
  "userCondition": {
    "douyinIds": ["68370542454", "test_user_002"],
    "starMapIds": ["6857515126107406344", "star_test_002"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}
```

### 2. 检查数据是否存在
调试接口会告诉您：
- 哪些ID在数据库中存在
- 哪些ID在数据库中不存在
- 实际查询到多少条记录

### 3. 逐个测试条件
分别测试每个条件：
```http
# 只测试抖音ID
{"userCondition": {"douyinIds": ["68370542454"]}}

# 只测试星图ID  
{"userCondition": {"starMapIds": ["6857515126107406344"]}}

# 只测试抖音链接
{"userCondition": {"douyinLinks": ["https://www.douyin.com/user/test_user_001"]}}
```

### 4. 检查表中是否有数据
```http
POST /api/oppo/page
{
  "pageNum": 1,
  "pageSize": 5
  // 不传任何查询条件，看看表中是否有数据
}
```

## 🎯 可能的原因和解决方案

### 1. 数据不存在
**原因**: 数据库中没有您查询的ID
**解决**: 使用调试接口检查哪些ID存在，使用存在的ID进行查询

### 2. 表名错误
**原因**: 查询的不是正确的周表
**解决**: 使用 `/api/oppo/latest-table` 检查当前使用的表名

### 3. 字段映射错误
**原因**: 数据库字段名与查询条件不匹配
**解决**: 检查数据库表结构，确认字段名正确

### 4. 数据格式问题
**原因**: ID格式不匹配（如数字vs字符串）
**解决**: 确保查询的ID格式与数据库中存储的格式一致

### 5. 权限问题
**原因**: Token无效或权限不足
**解决**: 检查Token是否有效，是否有查询权限

## 📈 修复效果

### 修复前（AND 逻辑）
```sql
-- 要求同时满足所有条件，几乎不可能有结果
WHERE douyin_id IN (...) AND star_map_id IN (...) AND douyin_link IN (...)
```

### 修复后（OR 逻辑）
```sql
-- 满足任意一个条件即可，大大增加查询成功率
WHERE (douyin_id IN (...) OR star_map_id IN (...) OR douyin_link IN (...))
```

## ⚠️ 注意事项

### 1. 查询范围扩大
使用 OR 逻辑后，查询结果可能比预期的多，因为满足任意一个条件的记录都会被返回。

### 2. 性能考虑
OR 查询可能比 AND 查询慢一些，但通常影响不大。

### 3. 业务逻辑
确认 OR 逻辑符合您的业务需求。如果需要 AND 逻辑，请明确说明具体的业务场景。

现在您可以使用修复后的查询逻辑和调试工具来解决查询返回 0 条记录的问题！
