# List<UserCondition> 改造实现

## 🎯 改造目标

将 `@PostMapping("/page")` 接口中的 `userCondition` 参数从单个对象改造为 `List<UserCondition>`，并且每个 `UserCondition` 只查询一条数据。

## ✅ 改造内容

### 1. 数据结构改造

#### 原有结构
```java
// 单个 UserQueryCondition 对象，包含多个列表
private UserQueryCondition userCondition;

public static class UserQueryCondition {
    private String userIds;                    // 逗号分隔字符串
    private List<String> userIdList;           // 用户ID列表
    private List<String> douyinIds;            // 抖音ID列表
    private List<String> starMapIds;           // 星图ID列表
    private List<String> douyinLinks;          // 抖音链接列表
}
```

#### 新的结构
```java
// UserCondition 列表，每个对象只查询一条数据
private List<UserCondition> userCondition;

public static class UserCondition {
    private String userId;                     // 单个用户ID
    private String douyinId;                   // 单个抖音ID
    private String starMapId;                  // 单个星图ID
    private String douyinLink;                 // 单个抖音链接
}
```

### 2. 核心设计理念

#### 每个 UserCondition 只查询一条数据
- 每个 `UserCondition` 对象只包含单个字段值
- 优先级：`douyinId` > `starMapId` > `userId` > `douyinLink`
- 如果一个对象包含多个字段，只使用优先级最高的字段

#### 查询逻辑
```sql
-- 生成的 SQL 查询逻辑
WHERE (
    (douyin_id = 'value1')
    OR (star_map_id = 'value2')
    OR (user_id = 'value3')
    OR (douyin_link = 'value4')
)
```

### 3. UserCondition 类设计

#### 核心方法
```java
public class UserCondition {
    // 获取有效的查询ID（按优先级）
    public String getEffectiveId() { ... }
    
    // 获取查询类型
    public String getQueryType() { ... }
    
    // 获取查询字段名
    public String getQueryFieldName() { ... }
    
    // 获取查询值
    public String getQueryValue() { ... }
    
    // 检查是否有有效条件
    public boolean hasValidCondition() { ... }
    
    // 验证条件有效性
    public String validate() { ... }
}
```

#### 构造函数
```java
public UserCondition() {}
public UserCondition(String userId) { ... }
public UserCondition(String douyinId, String starMapId) { ... }
public UserCondition(String userId, String douyinId, String starMapId, String douyinLink) { ... }
```

### 4. SQL 查询改造

#### 新的 SQL 逻辑
```xml
<!-- 处理用户查询条件列表（每个条件查询一条数据） -->
<if test="request.userCondition != null and request.userCondition.size() > 0">
    AND (
    <trim suffixOverrides="OR">
        <foreach collection="request.userCondition" item="condition" separator="OR">
            <if test="condition.hasValidCondition">
                (
                <trim suffixOverrides="OR">
                    <!-- 处理userId -->
                    <if test="condition.userId != null and condition.userId != ''">
                        user_id = #{condition.userId}
                        OR
                    </if>
                    
                    <!-- 处理douyinId -->
                    <if test="condition.douyinId != null and condition.douyinId != ''">
                        douyin_id = #{condition.douyinId}
                        OR
                    </if>
                    
                    <!-- 处理starMapId -->
                    <if test="condition.starMapId != null and condition.starMapId != ''">
                        star_map_id = #{condition.starMapId}
                        OR
                    </if>
                    
                    <!-- 处理douyinLink -->
                    <if test="condition.douyinLink != null and condition.douyinLink != ''">
                        douyin_link = #{condition.douyinLink}
                        OR
                    </if>
                </trim>
                )
            </if>
        </foreach>
    </trim>
    )
</if>
```

### 5. 向后兼容性

#### 兼容策略
```java
public List<UserCondition> getEffectiveUserConditions() {
    List<UserCondition> conditions = new ArrayList<>();
    
    // 优先使用新的 userCondition 列表
    if (userCondition != null && !userCondition.isEmpty()) {
        conditions.addAll(userCondition.stream()
            .filter(UserCondition::hasValidCondition)
            .collect(Collectors.toList()));
    }
    
    // 如果新的条件列表为空，从旧字段构建
    if (conditions.isEmpty()) {
        // 从 userIds 字符串构建
        if (StringUtils.hasText(this.userIds)) {
            String[] ids = this.userIds.split(",");
            for (String id : ids) {
                conditions.add(new UserCondition(id.trim()));
            }
        }
        
        // 从其他旧字段构建...
    }
    
    return conditions;
}
```

## 📊 使用方式

### 1. 新的推荐方式

#### 查询多个用户（每个条件查询一条数据）
```json
{
  "token": "your_token",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}
```

#### 查询单个用户
```json
{
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}
```

#### 查询多个相同类型的用户
```json
{
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "douyinId": "test_user_002"
    },
    {
      "douyinId": "test_user_003"
    }
  ]
}
```

### 2. 向后兼容方式

#### 原有方式仍然有效
```json
{
  "userIds": "68370542454,test_user_002",
  "douyinIds": ["douyin_test_001"],
  "starMapIds": ["6857515126107406344"]
}
```

## 🧪 调试功能

### 调试接口
```http
POST /api/oppo/debug-user-condition
```

### 调试响应示例
```json
{
  "success": true,
  "data": {
    "effectiveUserConditions": [
      {
        "douyinId": "68370542454",
        "queryType": "douyinId",
        "effectiveId": "68370542454"
      },
      {
        "starMapId": "6857515126107406344",
        "queryType": "starMapId",
        "effectiveId": "6857515126107406344"
      }
    ],
    "analysis": {
      "hasAnyUserCondition": true,
      "conditionCount": 2,
      "conditionDetails": [
        {
          "index": 1,
          "queryType": "douyinId",
          "queryFieldName": "douyin_id",
          "queryValue": "68370542454",
          "effectiveId": "68370542454",
          "hasValidCondition": true,
          "validation": null
        },
        {
          "index": 2,
          "queryType": "starMapId",
          "queryFieldName": "star_map_id",
          "queryValue": "6857515126107406344",
          "effectiveId": "6857515126107406344",
          "hasValidCondition": true,
          "validation": null
        }
      ]
    },
    "tableName": "oppo_data_2025_01",
    "actualCount": 2,
    "dataCheck": {
      "douyinId_68370542454": "存在",
      "starMapId_6857515126107406344": "存在"
    }
  }
}
```

## ⚠️ 注意事项

### 1. 数据量限制
- 最多支持 100 个 `UserCondition` 对象
- 每个 `UserCondition` 只查询一条数据
- 建议每个对象只提供一个查询字段

### 2. 优先级规则
当一个 `UserCondition` 包含多个字段时：
1. `douyinId` 优先级最高
2. `starMapId` 次之
3. `userId` 再次之
4. `douyinLink` 优先级最低

### 3. 验证规则
```java
// 每个 UserCondition 必须至少包含一个有效字段
public String validate() {
    if (!hasValidCondition()) {
        return "至少需要提供一个查询条件";
    }
    
    // 建议只提供一个字段
    int idCount = 0;
    if (StringUtils.hasText(userId)) idCount++;
    if (StringUtils.hasText(douyinId)) idCount++;
    if (StringUtils.hasText(starMapId)) idCount++;
    if (StringUtils.hasText(douyinLink)) idCount++;
    
    if (idCount > 1) {
        return "建议每个 UserCondition 只提供一个查询条件";
    }
    
    return null; // 验证通过
}
```

## 🎯 预期效果

### 改造前
```json
// 一个对象包含多个列表，查询逻辑复杂
"userCondition": {
    "douyinIds": ["id1", "id2"],
    "starMapIds": ["id3", "id4"]
}
```

### 改造后
```json
// 多个对象，每个只查询一条数据，逻辑清晰
"userCondition": [
    {"douyinId": "id1"},
    {"douyinId": "id2"},
    {"starMapId": "id3"},
    {"starMapId": "id4"}
]
```

### 优势
1. **逻辑清晰**：每个条件对应一条数据
2. **易于理解**：查询意图明确
3. **灵活性高**：可以混合不同类型的查询条件
4. **向后兼容**：不影响现有调用方式
5. **易于扩展**：新增查询类型只需在 `UserCondition` 中添加字段

现在您的分页查询接口支持 `List<UserCondition>` 结构，每个 `UserCondition` 只查询一条数据！
