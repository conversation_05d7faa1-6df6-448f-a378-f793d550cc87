# Excel 上传模板说明（包含 douyin_link 字段）

## 📋 Excel 文件格式要求

### 文件格式
- **支持格式**: `.xlsx` 或 `.xls`
- **工作表**: 使用第一个工作表
- **编码**: UTF-8

### 列结构（必须按顺序）

| 列序号 | 列名 | 字段名 | 数据类型 | 是否必填 | 说明 |
|--------|------|--------|----------|----------|------|
| A (第1列) | 抖音ID | douyin_id | 文本 | 是* | 用户的抖音ID |
| B (第2列) | 星图ID | star_map_id | 文本 | 是* | 用户的星图ID |
| C (第3列) | 抖音链接 | douyin_link | 文本 | 否 | 用户的抖音主页链接 |

**注意**: 抖音ID 和 星图ID 至少需要填写一个，不能都为空。

## 📝 Excel 模板示例

### 标题行（第1行）
```
抖音ID | 星图ID | 抖音链接
```

### 数据行示例
```
test_user_001 | star_test_001 | https://www.douyin.com/user/test_user_001
test_user_002 | star_test_002 | https://www.douyin.com/user/test_user_002
test_user_003 |               | https://www.douyin.com/user/test_user_003
              | star_test_004 | https://www.douyin.com/user/star_test_004
test_user_005 | star_test_005 |
```

## 🔍 字段详细说明

### 1. 抖音ID (douyin_id)
- **格式**: 文本字符串
- **长度**: 最大100字符
- **示例**: `test_user_001`, `douyin_user_123`
- **说明**: 用户在抖音平台的唯一标识

### 2. 星图ID (star_map_id)
- **格式**: 文本字符串
- **长度**: 最大100字符
- **示例**: `star_test_001`, `starmap_user_456`
- **说明**: 用户在星图平台的唯一标识

### 3. 抖音链接 (douyin_link) - **新增字段**
- **格式**: URL字符串
- **长度**: 最大500字符
- **示例**: 
  - `https://www.douyin.com/user/test_user_001`
  - `https://v.douyin.com/user/test_user_001`
  - `douyin://user/test_user_001`
- **说明**: 用户抖音主页的完整链接地址
- **可选**: 此字段为可选，可以为空

## ⚠️ 数据验证规则

### 1. 必填验证
- 抖音ID 和 星图ID 至少需要填写一个
- 如果两个ID都为空，该行数据会被跳过

### 2. 格式验证
- 所有字段会自动去除前后空格
- 空字符串会被转换为 NULL

### 3. 长度验证
- 抖音ID: 最大100字符
- 星图ID: 最大100字符
- 抖音链接: 最大500字符

### 4. 唯一性验证
- 抖音ID 在数据库中必须唯一
- 星图ID 在数据库中必须唯一
- 重复数据的处理方式取决于 `overwrite` 参数

## 🚀 上传方式

### 1. 默认去重模式 (overwrite=false)
```http
POST /api/oppo-user/upload-excel
Content-Type: multipart/form-data

file: your_excel_file.xlsx
token: your_auth_token
# overwrite 参数默认为 false
```

### 2. 覆盖模式 (overwrite=true)
```http
POST /api/oppo-user/upload-excel
Content-Type: multipart/form-data

file: your_excel_file.xlsx
token: your_auth_token
overwrite: true
```

## 📊 上传响应示例

```json
{
  "success": true,
  "code": "200",
  "message": "用户数据上传成功",
  "data": {
    "batchId": "EXCEL_20250106153000_001",
    "batchName": "Excel上传-test_users_with_douyin_link.xlsx",
    "tableName": "oppo_user_2025_01",
    "totalCount": 100,
    "successCount": 95,
    "failedCount": 5,
    "startTime": "2025-01-06T15:30:00",
    "endTime": "2025-01-06T15:30:15",
    "duration": "15秒"
  }
}
```

## 🔧 常见问题

### 1. 文件格式错误
**错误**: "文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件"
**解决**: 确保文件扩展名为 .xlsx 或 .xls

### 2. 数据为空
**错误**: "Excel 文件中没有找到有效的用户数据"
**解决**: 检查是否有数据行，确保抖音ID或星图ID至少有一个不为空

### 3. 重复数据
**行为**: 
- `overwrite=false` (默认): 重复数据会被忽略
- `overwrite=true`: 重复数据会被更新

### 4. 字段缺失
**说明**: 如果 Excel 文件只有2列（抖音ID、星图ID），系统仍然可以正常处理，douyin_link 字段会被设置为 NULL

## 📋 Excel 模板下载

您可以创建一个包含以下内容的 Excel 文件作为模板：

### 第1行（标题行）
| A | B | C |
|---|---|---|
| 抖音ID | 星图ID | 抖音链接 |

### 第2行（示例数据）
| A | B | C |
|---|---|---|
| test_user_001 | star_test_001 | https://www.douyin.com/user/test_user_001 |

### 第3行（示例数据）
| A | B | C |
|---|---|---|
| test_user_002 | star_test_002 | https://www.douyin.com/user/test_user_002 |

## 🎯 最佳实践

### 1. 数据准备
- 确保数据的准确性和完整性
- 使用标准的URL格式填写抖音链接
- 避免特殊字符和换行符

### 2. 批量上传
- 建议每次上传不超过1000条记录
- 使用合适的批次大小（默认100）
- 监控上传进度和结果

### 3. 错误处理
- 检查上传响应中的错误信息
- 对失败的记录进行单独处理
- 保留上传日志用于问题排查

### 4. 数据验证
- 上传后验证数据的完整性
- 使用查询接口确认数据正确入库
- 定期检查数据的一致性

现在您的 Excel 上传功能已经支持 douyin_link 字段了！
