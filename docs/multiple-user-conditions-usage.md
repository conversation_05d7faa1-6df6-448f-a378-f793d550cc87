# List<UserCondition> 多用户查询使用指南

## 🎯 设计理念

`List<UserCondition> userCondition` 的设计理念是：
- **一个 UserCondition = 一个用户的查询条件**
- **多个 UserCondition = 查询多个用户**
- **每个用户可以用不同的字段来标识**（douyin_id、user_id、douyin_link 等）

## 📊 UserCondition 字段说明

每个 `UserCondition` 对象包含以下字段：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `userId` | String | 用户ID（通用） | "user_123456" |
| `douyinId` | String | 抖音ID | "68370542454" |
| `starMapId` | String | 星图ID | "6857515126107406344" |
| `douyinLink` | String | 抖音链接 | "https://www.douyin.com/user/test_user_001" |

**注意**：每个 `UserCondition` 建议只填写一个字段，用于标识一个用户。

## 🔍 查询逻辑

### SQL 生成逻辑
当传入多个 `UserCondition` 时，会生成 OR 连接的查询条件：

```sql
SELECT * FROM oppo_data_table 
WHERE (
    (douyin_id = 'user1_douyin_id')      -- UserCondition 1
    OR (star_map_id = 'user2_star_id')   -- UserCondition 2
    OR (user_id = 'user3_user_id')       -- UserCondition 3
    OR (douyin_link = 'user4_link')      -- UserCondition 4
)
```

### 字段优先级
如果一个 `UserCondition` 包含多个字段，按以下优先级使用：
1. `douyinId` （最高优先级）
2. `starMapId`
3. `userId`
4. `douyinLink` （最低优先级）

## 📝 使用示例

### 1. 查询单个用户
```json
{
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}
```

### 2. 查询多个用户（不同字段类型）
```json
{
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "userId": "user_123456"
    },
    {
      "douyinLink": "https://www.douyin.com/user/test_user_001"
    }
  ]
}
```

### 3. 查询多个用户（相同字段类型）
```json
{
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "douyinId": "68370542455"
    },
    {
      "douyinId": "68370542456"
    }
  ]
}
```

### 4. 批量查询（混合类型）
```json
{
  "userCondition": [
    {"douyinId": "user1"},
    {"douyinId": "user2"},
    {"starMapId": "star1"},
    {"starMapId": "star2"},
    {"userId": "internal1"},
    {"douyinLink": "https://www.douyin.com/user/link1"}
  ]
}
```

## 🎯 实际应用场景

### 场景1：批量查询不同平台用户
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "userCondition": [
    {"douyinId": "抖音用户1"},
    {"douyinId": "抖音用户2"},
    {"starMapId": "星图用户1"},
    {"starMapId": "星图用户2"}
  ]
}
```

### 场景2：根据不同标识查询用户
```json
{
  "userCondition": [
    {"douyinId": "68370542454"},
    {"douyinLink": "https://www.douyin.com/user/test_user_001"},
    {"userId": "internal_user_123"}
  ]
}
```

### 场景3：结合其他查询条件
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "batchId": "EXCEL_20250106153000_001",
  "startUploadTime": "2025-01-01 00:00:00",
  "endUploadTime": "2025-01-31 23:59:59",
  "userCondition": [
    {"douyinId": "68370542454"},
    {"starMapId": "6857515126107406344"}
  ]
}
```

## ⚠️ 限制和注意事项

### 1. 数量限制
- 最多支持 **100个** `UserCondition` 对象
- 建议单次查询不超过 50 个用户

### 2. 字段使用建议
- **推荐**：每个 `UserCondition` 只填写一个字段
- **避免**：在同一个 `UserCondition` 中填写多个字段

### 3. 性能考虑
- 用户数量越多，查询时间越长
- 建议使用分页查询，合理设置 `pageSize`

### 4. 数据存在性
- 系统会查询所有匹配的用户
- 如果某个用户不存在，不会影响其他用户的查询结果

## 🧪 调试和验证

### 使用调试接口
```http
POST /api/oppo/debug-user-condition
{
  "userCondition": [
    {"douyinId": "68370542454"},
    {"starMapId": "6857515126107406344"}
  ]
}
```

### 调试响应示例
```json
{
  "success": true,
  "data": {
    "effectiveUserConditions": [
      {
        "douyinId": "68370542454",
        "queryType": "douyinId",
        "effectiveId": "68370542454"
      },
      {
        "starMapId": "6857515126107406344",
        "queryType": "starMapId",
        "effectiveId": "6857515126107406344"
      }
    ],
    "analysis": {
      "hasAnyUserCondition": true,
      "conditionCount": 2,
      "conditionDetails": [...]
    },
    "actualCount": 2,
    "dataCheck": {
      "douyinId_68370542454": "存在",
      "starMapId_6857515126107406344": "存在"
    }
  }
}
```

## 🔄 向后兼容

### 原有方式仍然支持
```json
{
  "userIds": "68370542454,test_user_002",
  "douyinIds": ["douyin_test_001"],
  "starMapIds": ["6857515126107406344"]
}
```

### 自动转换逻辑
系统会自动将原有字段转换为 `UserCondition` 列表：
- `userIds: "id1,id2,id3"` → `[{userId:"id1"}, {userId:"id2"}, {userId:"id3"}]`
- `douyinIds: ["id1","id2"]` → `[{douyinId:"id1"}, {douyinId:"id2"}]`

## 📈 最佳实践

### 1. 推荐的查询方式
```json
// ✅ 推荐：清晰明确
{
  "userCondition": [
    {"douyinId": "68370542454"},
    {"starMapId": "6857515126107406344"}
  ]
}
```

### 2. 不推荐的方式
```json
// ❌ 不推荐：一个对象包含多个字段
{
  "userCondition": [
    {
      "douyinId": "68370542454",
      "starMapId": "6857515126107406344"  // 会被忽略
    }
  ]
}
```

### 3. 批量查询建议
```json
// ✅ 推荐：合理的批次大小
{
  "pageNum": 1,
  "pageSize": 20,
  "userCondition": [
    // 10-20个用户条件
  ]
}
```

## 🎯 总结

`List<UserCondition> userCondition` 提供了灵活、清晰的多用户查询方式：

- **灵活性**：支持不同类型的用户标识
- **清晰性**：一个条件对应一个用户
- **扩展性**：易于添加新的用户标识类型
- **兼容性**：完全向后兼容原有接口

这种设计让您可以轻松地查询多个用户，无论他们使用什么样的标识符！
