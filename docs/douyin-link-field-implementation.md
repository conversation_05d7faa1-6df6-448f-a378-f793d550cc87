# douyin_link 字段完整实现

## 🎯 实现目标

为 `@PostMapping("/upload-excel")` 接口上传新增一个 `douyin_link` 字段，并在代码和数据库表中完整实现此字段。

## ✅ 完整实现清单

### 1. 数据库层修改

#### 1.1 实体类 (OppoUser.java)
```java
// 新增字段
private String douyinLink;

// 新增构造函数
public OppoUser(String douyinId, String starMapId, String douyinLink, String batchId, String uploadSource)

// 新增 getter/setter 方法
public String getDouyinLink() { return douyinLink; }
public void setDouyinLink(String douyinLink) { this.douyinLink = douyinLink; }
```

#### 1.2 数据库表结构修改
```sql
-- 为所有现有周表添加字段
ALTER TABLE `oppo_user_2025_XX` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;

-- 更新表创建脚本和存储过程
CREATE TABLE ... (
  `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接',
  ...
)
```

#### 1.3 Mapper XML 修改
```xml
<!-- 批量插入语句添加 douyin_link 字段 -->
INSERT INTO ${tableName} (douyin_id, star_map_id, douyin_link, ...)
VALUES (#{item.douyinId}, #{item.starMapId}, #{item.douyinLink}, ...)

<!-- ON DUPLICATE KEY UPDATE 添加字段更新 -->
ON DUPLICATE KEY UPDATE
douyin_link = VALUES(douyin_link),
...
```

### 2. 业务逻辑层修改

#### 2.1 DTO 类 (OppoUserUploadRequest.java)
```java
// OppoUserData 内部类新增字段
private String douyinLink;

// 新增构造函数
public OppoUserData(String douyinId, String starMapId, String douyinLink)

// 新增 getter/setter 方法
public String getDouyinLink() { return douyinLink; }
public void setDouyinLink(String douyinLink) { this.douyinLink = douyinLink; }
```

#### 2.2 Service 层 (OppoUserService.java)
```java
// convertToOppoUsersWithBatch 方法添加字段设置
user.setDouyinLink(data.getDouyinLink());
```

#### 2.3 工具类 (ExcelUtil.java)
```java
// readUserDataFromRow 方法添加第三列读取
String douyinLink = getCellValueAsString(row.getCell(2));
userData.setDouyinLink(StringUtils.hasText(douyinLink) ? douyinLink.trim() : null);
```

### 3. 数据库脚本

#### 3.1 字段添加脚本 (add_douyin_link_field.sql)
- 为所有现有周表添加 `douyin_link` 字段
- 更新存储过程确保新建表包含此字段
- 提供批量添加和验证功能

#### 3.2 表创建脚本更新 (create_oppo_user_weekly_table.sql)
- 更新基础表结构定义
- 更新存储过程模板

## 📊 字段规格

### 数据库字段
- **字段名**: `douyin_link`
- **数据类型**: `varchar(500)`
- **默认值**: `NULL`
- **注释**: '抖音链接'
- **位置**: 在 `star_map_id` 字段之后

### Java 字段
- **字段名**: `douyinLink`
- **数据类型**: `String`
- **可空**: `true`
- **最大长度**: 500字符

## 🔄 Excel 文件格式

### 列结构（更新后）
| 列序号 | 列名 | 字段名 | 数据类型 | 是否必填 |
|--------|------|--------|----------|----------|
| A (第1列) | 抖音ID | douyin_id | 文本 | 是* |
| B (第2列) | 星图ID | star_map_id | 文本 | 是* |
| C (第3列) | 抖音链接 | douyin_link | 文本 | 否 |

**注意**: 抖音ID 和 星图ID 至少需要填写一个。

### Excel 示例
```
抖音ID          | 星图ID         | 抖音链接
test_user_001   | star_test_001  | https://www.douyin.com/user/test_user_001
test_user_002   | star_test_002  | https://www.douyin.com/user/test_user_002
test_user_003   |                | https://www.douyin.com/user/test_user_003
```

## 🧪 测试验证

### 1. 数据库验证
```sql
-- 检查字段是否添加成功
DESCRIBE `oppo_user_2025_01`;

-- 验证数据插入
SELECT douyin_id, star_map_id, douyin_link FROM `oppo_user_2025_01` LIMIT 5;
```

### 2. API 测试
```http
# Excel 上传测试
POST /api/oppo-user/upload-excel
Content-Type: multipart/form-data
file: test_users_with_douyin_link.xlsx

# API 上传测试
POST /api/oppo-user/upload
{
  "users": [
    {
      "douyinId": "test_001",
      "starMapId": "star_001", 
      "douyinLink": "https://www.douyin.com/user/test_001"
    }
  ]
}

# 查询验证
GET /api/oppo-user/douyin/test_001
```

## 🔧 部署步骤

### 1. 数据库修改
```bash
# 执行字段添加脚本
mysql -u username -p database_name < sql/add_douyin_link_field.sql
```

### 2. 应用重启
```bash
# 重启应用以加载新的代码
systemctl restart oppo-data-api
```

### 3. 功能验证
```bash
# 使用测试文件验证功能
curl -X POST http://localhost:10000/api/oppo-user/upload-excel \
  -F "file=@test_users_with_douyin_link.xlsx" \
  -F "token=your_token"
```

## 📈 影响评估

### 1. 向后兼容性
- ✅ **完全兼容**: 现有的2列Excel文件仍然可以正常上传
- ✅ **API兼容**: 现有API调用不受影响
- ✅ **数据兼容**: 现有数据不受影响

### 2. 性能影响
- ✅ **最小影响**: 新增字段对查询性能影响很小
- ✅ **存储增加**: 每条记录增加最多500字节存储
- ✅ **索引优化**: 暂不需要为此字段添加索引

### 3. 功能增强
- ✅ **数据完整性**: 提供更完整的用户信息
- ✅ **业务扩展**: 支持基于链接的业务逻辑
- ✅ **用户体验**: 提供更丰富的数据维度

## ⚠️ 注意事项

### 1. 数据验证
- 字段为可选，可以为空
- 建议验证URL格式的有效性
- 注意字符串长度限制（500字符）

### 2. 安全考虑
- 验证URL的安全性，避免恶意链接
- 考虑对链接进行格式化和标准化
- 记录数据来源和修改历史

### 3. 维护建议
- 定期检查链接的有效性
- 监控字段的使用情况
- 考虑未来可能的字段扩展

## 🎯 预期效果

实现完成后：
1. ✅ **Excel上传支持3列**: 抖音ID、星图ID、抖音链接
2. ✅ **API上传支持**: JSON格式包含douyinLink字段
3. ✅ **数据库存储**: 所有用户表包含douyin_link字段
4. ✅ **查询返回**: 所有查询接口返回douyin_link字段
5. ✅ **向后兼容**: 现有功能不受影响

现在您的 Excel 上传接口已经完全支持 `douyin_link` 字段了！
