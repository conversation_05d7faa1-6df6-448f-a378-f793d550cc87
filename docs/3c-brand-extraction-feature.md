# 3C品牌提取功能实现

## 🎯 功能目标

从商业合作品牌字段（`kw_brand`）中提取3C品牌关键词，并将匹配的品牌写入 `threeCCommercialCooperationBrands` 字段中，实现去重并合并到 `pageData` 中。

## 📋 支持的3C品牌

系统支持以下3C品牌关键词的识别：

| 品牌类别 | 关键词 |
|----------|--------|
| **vivo** | vivo |
| **小米** | 小米, xiaomi |
| **OPPO** | OPPO |
| **华为** | 华为, huawei |
| **三星** | 三星, Samsung |
| **红米** | 红米, redmi |
| **一加** | 一加, oneplus |
| **荣耀** | 荣耀, HONOR |
| **苹果** | 苹果, iPhone, Apple |

## 🔧 实现逻辑

### 1. 核心方法：`extractThreeCBrands`

```java
/**
 * 提取3C品牌
 * @param kwBrand 商业合作品牌字符串
 * @return 匹配的3C品牌，用逗号分隔
 */
private String extractThreeCBrands(String kwBrand) {
    if (kwBrand == null || kwBrand.trim().isEmpty()) {
        return "";
    }
    
    // 定义3C品牌关键词（不区分大小写）
    String[] threeCBrandKeywords = {
        "vivo", "小米", "xiaomi", "OPPO", "华为", "huawei", 
        "三星", "Samsung", "红米", "redmi", "一加", "oneplus", 
        "荣耀", "HONOR", "苹果", "iPhone", "Apple"
    };
    
    // 使用LinkedHashSet保持顺序并去重
    Set<String> matchedBrands = new LinkedHashSet<>();
    
    // 将kwBrand转换为小写进行匹配
    String kwBrandLower = kwBrand.toLowerCase();
    
    // 遍历所有3C品牌关键词
    for (String keyword : threeCBrandKeywords) {
        String keywordLower = keyword.toLowerCase();
        
        // 检查是否包含该关键词
        if (kwBrandLower.contains(keywordLower)) {
            // 添加原始关键词（保持原有大小写）
            matchedBrands.add(keyword);
        }
    }
    
    // 将匹配的品牌用逗号连接
    String result = String.join(",", matchedBrands);
    
    if (!result.isEmpty()) {
        logger.info("提取到3C合作品牌: {} (来源: {})", result, kwBrand);
    }
    
    return result;
}
```

### 2. 数据更新逻辑

```java
// 在处理每条记录时
String kwBrand = (String) sourceMap.get("kw_brand");
if (kwBrand == null) {
    logger.warn("kwBrand为空，跳过此条记录");
    continue;
}

// 提取3C品牌
String threeCCommercialCooperationBrands = extractThreeCBrands(kwBrand);

// 更新到pageData中
updateOppoData(userId, businessCooperationBrand, threeCCommercialCooperationBrands, pageData);
```

### 3. 更新方法修改

```java
private void updateOppoData(String userId, String businessCooperationBrand, 
                          String threeCCommercialCooperationBrands, List<OppoData> pageData) {
    for (OppoData oppoData : pageData) {
        if (userId.equals(oppoData.getUserId())) {
            // 设置商业合作品牌
            oppoData.setCommercialCooperationBrands(businessCooperationBrand);
            
            // 设置3C合作品牌
            oppoData.setThreeCCommercialCooperationBrands(threeCCommercialCooperationBrands);
            
            return;
        }
    }
}
```

## 📊 处理示例

### 示例1：单个品牌
```
输入: "我使用的是iPhone手机"
输出: "iPhone"
```

### 示例2：多个品牌
```
输入: "我有iPhone和小米手机"
输出: "iPhone,小米"
```

### 示例3：大小写混合
```
输入: "OPPO和huawei都很好用"
输出: "OPPO,huawei"
```

### 示例4：包含重复品牌（去重）
```
输入: "小米手机和xiaomi都是同一个品牌"
输出: "小米,xiaomi"
```

### 示例5：不包含3C品牌
```
输入: "我喜欢可口可乐和百事可乐"
输出: ""
```

### 示例6：复杂文本
```
输入: "今天我在商场看到了vivo、OPPO、华为和Samsung的新款手机，还有苹果的iPhone"
输出: "vivo,OPPO,华为,Samsung,苹果,iPhone"
```

## 🔍 特性说明

### 1. 不区分大小写匹配
- 输入文本会转换为小写进行匹配
- 但输出保持原始关键词的大小写

### 2. 自动去重
- 使用 `LinkedHashSet` 确保相同品牌不会重复
- 保持品牌出现的顺序

### 3. 容错处理
- 处理 `null` 值和空字符串
- 处理只包含空格的字符串

### 4. 性能优化
- 使用简单的字符串包含匹配，性能较好
- 适合处理大量数据

## 📈 数据流程

```
1. 从 sourceMap 获取 kw_brand 字段
   ↓
2. 检查 kw_brand 是否为空
   ↓
3. 调用 extractThreeCBrands() 提取3C品牌
   ↓
4. 将结果传递给 updateOppoData() 方法
   ↓
5. 更新 OppoData 对象的 threeCCommercialCooperationBrands 字段
   ↓
6. 合并到 pageData 中返回给前端
```

## 🧪 测试验证

### 单元测试
- 创建了 `ThreeCBrandExtractionTest` 类
- 包含多种测试场景：单个品牌、多个品牌、边界情况、性能测试

### 集成测试
- 使用 `test-3c-brand-extraction.http` 文件进行接口测试
- 验证完整的数据流程

### 测试用例
```java
@Test
public void testExtractThreeCBrands() {
    assertEquals("iPhone", extractThreeCBrands("我使用的是iPhone手机"));
    assertEquals("iPhone,小米", extractThreeCBrands("我有iPhone和小米手机"));
    assertEquals("OPPO,huawei", extractThreeCBrands("OPPO和huawei都很好用"));
    assertEquals("", extractThreeCBrands("我喜欢可口可乐"));
}
```

## 📋 数据库字段

### OppoData 实体类
```java
/**
 * 3C - 商业合作品牌：暂无描述
 */
private String threeCCommercialCooperationBrands;
```

### 字段说明
- **字段名**: `threeCCommercialCooperationBrands`
- **类型**: `String`
- **格式**: 逗号分隔的品牌名称
- **示例**: `"iPhone,小米,OPPO"`

## ⚠️ 注意事项

### 1. 匹配精度
- 使用简单的字符串包含匹配
- 可能会有误匹配的情况（如"小米粥"会匹配到"小米"）
- 如需更精确匹配，可考虑使用正则表达式

### 2. 品牌列表维护
- 3C品牌关键词列表需要定期更新
- 新增品牌时需要修改 `threeCBrandKeywords` 数组

### 3. 性能考虑
- 当前实现对于大量数据处理性能良好
- 如果品牌关键词列表很长，可考虑使用更高效的匹配算法

### 4. 日志记录
- 成功提取时会记录 INFO 级别日志
- 调试时会记录 DEBUG 级别日志
- 便于问题排查和数据分析

## 🎯 预期效果

实现后，系统将能够：
1. ✅ **自动识别**：从商业合作品牌中自动识别3C品牌
2. ✅ **去重处理**：相同品牌只保留一次
3. ✅ **格式统一**：用逗号分隔多个品牌
4. ✅ **数据完整**：将结果合并到返回的数据中
5. ✅ **日志记录**：记录提取过程便于监控

现在您的系统可以智能地从商业合作品牌中提取3C品牌信息了！
