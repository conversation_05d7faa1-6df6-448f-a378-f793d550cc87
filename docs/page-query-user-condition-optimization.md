# 分页查询用户条件参数优化

## 🎯 优化目标

将 `@PostMapping("/page")` 接口中的多个用户ID相关参数（`userIds`、`userIdList`、`douyinIds`、`starMapIds`）以及新增的 `douyinLinks` 字段合成一个统一的 `userCondition` 对象，使查询时可以传任何参数组合，提高接口的灵活性和可维护性。

## ✅ 优化内容

### 1. 新增 UserQueryCondition 内部类

```java
@Data
public static class UserQueryCondition {
    /**
     * 用户ID列表（逗号分隔的字符串，兼容原有接口）
     */
    private String userIds;

    /**
     * 用户ID列表（数组形式，支持抖音ID和星图ID混合）
     */
    private List<String> userIdList;

    /**
     * 抖音ID列表
     */
    private List<String> douyinIds;

    /**
     * 星图ID列表
     */
    private List<String> starMapIds;

    /**
     * 抖音链接列表（新增）
     */
    private List<String> douyinLinks;
    
    // 提供多种便捷方法
    public List<String> getAllUserIds() { ... }
    public List<String> getAllDouyinIds() { ... }
    public List<String> getAllStarMapIds() { ... }
    public List<String> getAllDouyinLinks() { ... }
    public boolean hasAnyUserCondition() { ... }
}
```

### 2. PageQueryRequest 类优化

#### 新增字段
```java
/**
 * 用户查询条件统一对象（推荐使用）
 */
private UserQueryCondition userCondition;
```

#### 原有字段标记为 @Deprecated
```java
/**
 * @deprecated 建议使用 userCondition.userIds
 */
@Deprecated
private String userIds;

/**
 * @deprecated 建议使用 userCondition.userIdList
 */
@Deprecated
private List<String> userIdList;

/**
 * @deprecated 建议使用 userCondition.douyinIds
 */
@Deprecated
private List<String> douyinIds;

/**
 * @deprecated 建议使用 userCondition.starMapIds
 */
@Deprecated
private List<String> starMapIds;
```

#### 新增便捷方法
```java
/**
 * 获取有效的用户查询条件（兼容新旧两种方式）
 */
public UserQueryCondition getEffectiveUserCondition() { ... }

/**
 * 检查是否有任何用户查询条件
 */
public boolean hasAnyUserCondition() { ... }
```

### 3. SQL 查询优化

#### 支持新的 userCondition 对象
```xml
<!-- 处理用户查询条件（新的统一方式） -->
<if test="request.userCondition != null">
    <!-- 处理userCondition中的各种ID类型 -->
    <if test="request.userCondition.douyinIds != null and request.userCondition.douyinIds.size() > 0">
        AND (douyin_id IN
        <foreach collection="request.userCondition.douyinIds" item="douyinId" open="(" separator="," close=")">
            #{douyinId}
        </foreach>
        )
    </if>
    
    <!-- 处理userCondition中的抖音链接列表 -->
    <if test="request.userCondition.douyinLinks != null and request.userCondition.douyinLinks.size() > 0">
        AND (douyin_link IN
        <foreach collection="request.userCondition.douyinLinks" item="douyinLink" open="(" separator="," close=")">
            #{douyinLink}
        </foreach>
        )
    </if>
</if>
```

#### 保持向后兼容
```xml
<!-- 处理原有字段 - 向后兼容 -->
<if test="request.userCondition == null and request.userIds != null and request.userIds != ''">
    AND (user_id IN
    <foreach collection="request.userIds.split(',')" item="userId" open="(" separator="," close=")">
        #{userId}
    </foreach>
    )
</if>
```

## 📊 使用方式对比

### 1. 新的推荐方式（使用 userCondition）

#### 基本查询
```json
{
  "token": "your_token",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": {
    "douyinIds": ["test_user_001", "test_user_002"],
    "starMapIds": ["star_test_001"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}
```

#### 混合查询（所有字段）
```json
{
  "token": "your_token",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": {
    "userIds": "test_user_001,test_user_002",
    "userIdList": ["test_user_003", "test_user_004"],
    "douyinIds": ["douyin_test_001"],
    "starMapIds": ["star_test_001"],
    "douyinLinks": [
      "https://www.douyin.com/user/test_user_001",
      "https://v.douyin.com/user/test_user_002"
    ]
  }
}
```

#### 只使用抖音链接查询
```json
{
  "token": "your_token",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": {
    "douyinLinks": [
      "https://www.douyin.com/user/test_user_001",
      "https://www.douyin.com/user/test_user_002"
    ]
  }
}
```

### 2. 原有方式（向后兼容）

```json
{
  "token": "your_token",
  "pageNum": 1,
  "pageSize": 10,
  "userIds": "test_user_001,test_user_002",
  "douyinIds": ["douyin_test_001"],
  "starMapIds": ["star_test_001"]
}
```

## 🔄 兼容性策略

### 1. 优先级规则
- 如果提供了 `userCondition` 对象，优先使用 `userCondition` 中的条件
- 如果没有提供 `userCondition` 或 `userCondition` 为空，则使用原有的字段

### 2. 字段合并逻辑
`UserQueryCondition.getAllUserIds()` 方法会自动合并：
- `userIds` 字符串（逗号分隔）
- `userIdList` 数组
- `douyinIds` 数组
- `starMapIds` 数组

### 3. 去重处理
所有获取方法都会自动去重，确保查询条件的唯一性。

## 🎯 优势

### 1. 统一性
- 所有用户相关的查询条件都集中在一个对象中
- 减少了参数的复杂性和混乱

### 2. 灵活性
- 可以传递任意组合的参数
- 支持新增的 `douyinLinks` 字段查询
- 支持多种ID类型的混合查询

### 3. 可扩展性
- 未来新增用户相关的查询条件只需在 `UserQueryCondition` 中添加
- 不会影响主接口的参数结构

### 4. 向后兼容
- 原有的调用方式完全不受影响
- 渐进式迁移，不需要一次性修改所有调用方

### 5. 类型安全
- 强类型的对象结构，减少参数错误
- IDE 自动补全和类型检查

## 📝 迁移建议

### 1. 新项目
直接使用 `userCondition` 对象，享受更好的类型安全和可维护性。

### 2. 现有项目
- 保持现有调用方式不变
- 新功能逐步采用 `userCondition` 方式
- 在合适的时机统一迁移

### 3. 最佳实践
```json
// 推荐：使用 userCondition 对象
{
  "userCondition": {
    "douyinIds": ["user1", "user2"],
    "douyinLinks": ["https://www.douyin.com/user/user1"]
  }
}

// 不推荐：混用新旧方式
{
  "userCondition": {
    "douyinIds": ["user1"]
  },
  "starMapIds": ["star1"]  // 应该放在 userCondition 中
}
```

## 🧪 测试验证

### 1. 功能测试
- 新的 `userCondition` 对象各种组合查询
- 原有字段的向后兼容性
- 混合查询的正确性

### 2. 性能测试
- 大量ID查询的性能
- SQL 查询优化效果
- 内存使用情况

### 3. 边界测试
- 空参数处理
- 大量参数处理
- 特殊字符处理

## ⚠️ 注意事项

### 1. 参数验证
- 用户ID总数不能超过1000个
- 抖音链接格式验证
- 空值和null值处理

### 2. 性能考虑
- 大量ID查询时的SQL性能
- 内存使用优化
- 查询缓存策略

### 3. 安全考虑
- 参数注入防护
- 权限验证
- 日志脱敏

现在您的分页查询接口支持更灵活和统一的用户条件查询了！
