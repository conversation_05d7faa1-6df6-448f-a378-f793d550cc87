2025-07-04 13:41:02.207 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 13:41:02.291 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 24296 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 13:41:02.291 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 13:41:02.292 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 13:41:02.379 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 13:41:02.379 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 13:41:03.506 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 13:41:03.510 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 13:41:03.567 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-07-04 13:41:03.895 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 13:41:04.875 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 13:41:04.896 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 13:41:04.899 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 13:41:04.900 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 13:41:04.987 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 13:41:04.989 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2608 ms
2025-07-04 13:41:05.351 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 13:41:05.466 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 13:41:06.256 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 13:41:06.324 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 13:41:06.799 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 13:41:06.804 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 13:41:06.804 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 13:41:07.203 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 13:41:07.870 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 13:41:07.871 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 13:41:08.466 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 13:41:08.477 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 13:41:08.493 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.133 seconds (process running for 8.188)
2025-07-04 13:41:08.498 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 13:41:08.499 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 13:41:08.499 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 13:41:08.499 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 13:41:08.500 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 13:41:08.500 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 13:41:08.501 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 13:41:08.501 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 13:41:08.501 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 14:08:51.474 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 14:08:51.588 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 23988 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 14:08:51.589 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 14:08:51.590 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 14:08:51.692 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 14:08:51.692 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 14:08:52.800 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 14:08:52.803 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 14:08:52.855 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-07-04 14:08:53.171 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 14:08:54.106 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 14:08:54.128 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 14:08:54.131 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 14:08:54.132 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 14:08:54.220 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 14:08:54.222 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2527 ms
2025-07-04 14:08:54.577 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 14:08:54.692 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 14:08:55.539 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 14:08:55.606 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 14:08:56.141 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 14:08:56.146 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 14:08:56.147 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 14:08:56.573 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 14:08:57.185 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 14:08:57.186 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 14:08:57.700 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 14:08:57.711 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 14:08:57.727 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.126 seconds (process running for 8.198)
2025-07-04 14:08:57.731 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 14:08:57.731 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 14:08:57.731 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 14:08:57.731 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 14:08:57.732 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 14:08:57.732 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 14:08:57.733 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 14:08:57.733 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 14:08:57.733 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 15:36:26.849 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 15:36:26.933 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 22292 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 15:36:26.934 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 15:36:26.935 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 15:36:27.021 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 15:36:27.021 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 15:36:28.219 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 15:36:28.223 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 15:36:28.281 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-04 15:36:28.619 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 15:36:29.626 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 15:36:29.658 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 15:36:29.662 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 15:36:29.662 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 15:36:29.759 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 15:36:29.760 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2737 ms
2025-07-04 15:36:30.131 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 15:36:30.248 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 15:36:31.120 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 15:36:31.182 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 15:36:31.690 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 15:36:31.694 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 15:36:31.694 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 15:36:32.131 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 15:36:32.818 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 15:36:32.819 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 15:36:33.428 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 15:36:33.440 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 15:36:33.459 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.468 seconds (process running for 8.466)
2025-07-04 15:36:33.463 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 15:36:33.463 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 15:36:33.463 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 15:36:33.463 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 15:36:33.464 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 15:36:33.465 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 15:36:33.465 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 15:36:33.465 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 15:36:33.465 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 15:36:33.465 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 15:36:34.685 [http-nio-10000-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 15:36:34.685 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 15:36:34.686 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 15:36:34.689 [http-nio-10000-exec-1] INFO  data.oppodataapi.global.TokenFilter - contentType -> application/json, uri->/api/oppo/page 
2025-07-04 15:36:35.038 [http-nio-10000-exec-1] INFO  d.o.controller.OppoDataController - 接收到优化的分页查询请求
2025-07-04 15:36:35.038 [http-nio-10000-exec-1] DEBUG d.o.service.TokenValidationService - Token校验成功
2025-07-04 15:36:35.039 [http-nio-10000-exec-1] DEBUG d.o.service.OppoDataService - 开始查找最新的有数据的周表
2025-07-04 15:36:35.042 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-07-04 对应的表名: oppo_data_2025_27
2025-07-04 15:36:35.042 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-06-27 对应的表名: oppo_data_2025_26
2025-07-04 15:36:35.042 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-06-20 对应的表名: oppo_data_2025_25
2025-07-04 15:36:35.042 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-06-13 对应的表名: oppo_data_2025_24
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-06-06 对应的表名: oppo_data_2025_23
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-05-30 对应的表名: oppo_data_2025_22
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-05-23 对应的表名: oppo_data_2025_21
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-05-16 对应的表名: oppo_data_2025_20
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-05-09 对应的表名: oppo_data_2025_19
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-05-02 对应的表名: oppo_data_2025_18
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-04-25 对应的表名: oppo_data_2025_17
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 日期 2025-04-18 对应的表名: oppo_data_2025_16
2025-07-04 15:36:35.043 [http-nio-10000-exec-1] DEBUG d.oppodataapi.util.WeeklyTableUtil - 最近 12 周的表名: [oppo_data_2025_27, oppo_data_2025_26, oppo_data_2025_25, oppo_data_2025_24, oppo_data_2025_23, oppo_data_2025_22, oppo_data_2025_21, oppo_data_2025_20, oppo_data_2025_19, oppo_data_2025_18, oppo_data_2025_17, oppo_data_2025_16]
2025-07-04 15:36:35.068 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 15:36:36.206 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b8a698a
2025-07-04 15:36:36.208 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 15:36:36.214 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:36:36.239 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_data_2025_27(String)
2025-07-04 15:36:36.464 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:36:36.491 [http-nio-10000-exec-1] DEBUG d.o.m.O.getTableRecordCount - ==>  Preparing: SELECT COUNT(*) FROM oppo_data_2025_27
2025-07-04 15:36:36.491 [http-nio-10000-exec-1] DEBUG d.o.m.O.getTableRecordCount - ==> Parameters: 
2025-07-04 15:36:36.968 [http-nio-10000-exec-1] DEBUG d.o.m.O.getTableRecordCount - <==      Total: 1
2025-07-04 15:36:36.968 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 找到最新的有数据周表: oppo_data_2025_27, 记录数: 802
2025-07-04 15:36:36.968 [http-nio-10000-exec-1] INFO  d.o.controller.OppoDataController - 未指定周表，自动查找到最新有数据的周表: oppo_data_2025_27
2025-07-04 15:36:36.969 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:36:36.969 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_data_2025_27(String)
2025-07-04 15:36:36.994 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:36:36.994 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 开始优化分页查询OPPO数据，表名: oppo_data_2025_27
2025-07-04 15:36:37.031 [http-nio-10000-exec-1] DEBUG d.o.m.OppoDataMapper.countOptimized - ==>  Preparing: SELECT COUNT(*) FROM oppo_data_2025_27 WHERE ( ( douyin_id = ? ) OR ( star_map_id = ? ) OR ( douyin_link = ? ) OR ( douyin_id = ? ) OR ( user_id = ? ) )
2025-07-04 15:36:37.032 [http-nio-10000-exec-1] DEBUG d.o.m.OppoDataMapper.countOptimized - ==> Parameters: 1194698768(String), 7255920825218367548(String), https://www.douyin.com/user/MS4wLjABAAAAPDKpYN_dy9a-s_S-frXiusSLMa4CFOoN3JlHvPEdMmA(String), 374104447(String), 24009340848(String)
2025-07-04 15:36:37.170 [http-nio-10000-exec-1] DEBUG d.o.m.OppoDataMapper.countOptimized - <==      Total: 1
2025-07-04 15:36:37.174 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectPageOptimized - ==>  Preparing: SELECT * FROM oppo_data_2025_27 WHERE ( ( douyin_id = ? ) OR ( star_map_id = ? ) OR ( douyin_link = ? ) OR ( douyin_id = ? ) OR ( user_id = ? ) ) ORDER BY updated_time DESC LIMIT ? OFFSET ?
2025-07-04 15:36:37.177 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectPageOptimized - ==> Parameters: 1194698768(String), 7255920825218367548(String), https://www.douyin.com/user/MS4wLjABAAAAPDKpYN_dy9a-s_S-frXiusSLMa4CFOoN3JlHvPEdMmA(String), 374104447(String), 24009340848(String), 5(Integer), 0(Integer)
2025-07-04 15:36:37.805 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectPageOptimized - <==      Total: 5
2025-07-04 15:36:37.806 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 优化分页查询完成，表: oppo_data_2025_27, 总计: 5 条，当前页: 5 条
2025-07-04 15:36:37.807 [http-nio-10000-exec-1] INFO  d.o.controller.OppoDataController - 优化分页查询成功，返回 5 条记录，总计 5 条，表: oppo_data_2025_27
2025-07-04 15:41:39.339 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-04 15:41:39.345 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-04 15:48:19.425 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 15:48:19.517 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 8248 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 15:48:19.518 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 15:48:19.518 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 15:48:19.607 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 15:48:19.608 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 15:48:20.772 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 15:48:20.776 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 15:48:20.836 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-07-04 15:48:21.175 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 15:48:22.139 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 15:48:22.161 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 15:48:22.167 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 15:48:22.168 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 15:48:22.261 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 15:48:22.262 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2653 ms
2025-07-04 15:48:22.605 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 15:48:22.707 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 15:48:23.438 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 15:48:23.518 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 15:48:23.996 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 15:48:24.000 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 15:48:24.000 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 15:48:24.383 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 15:48:24.916 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 15:48:24.917 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 15:48:25.478 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 15:48:25.489 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 15:48:25.506 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.066 seconds (process running for 8.41)
2025-07-04 15:48:25.510 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 15:48:25.510 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 15:48:25.511 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 15:48:25.511 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 15:48:25.512 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 15:48:25.512 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 15:48:25.512 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 15:48:25.512 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 15:48:25.512 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 15:48:26.977 [http-nio-10000-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 15:48:26.978 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 15:48:26.979 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 15:48:26.982 [http-nio-10000-exec-1] INFO  data.oppodataapi.global.TokenFilter - contentType -> multipart/form-data; boundary=--------------------------639186395929061074370185, uri->/api/oppo/user/upload-excel 
2025-07-04 15:48:27.081 [http-nio-10000-exec-1] INFO  d.o.controller.OppoUserController - 接收到 Excel 文件上传请求，文件名: oppo_user_template (6).xlsx, 大小: 9670 bytes
2025-07-04 15:48:27.126 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 15:48:28.091 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3d98dfa8
2025-07-04 15:48:28.093 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 15:48:28.100 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.insert - ==>  Preparing: INSERT INTO oppo_user_batch ( batch_id, batch_name, upload_source, file_name, file_size, total_count, success_count, failed_count, upload_status, created_by ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-04 15:48:28.126 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.insert - ==> Parameters: EXCEL_20250704154827_001(String), Excel上传 - oppo_user_template (6).xlsx (2025-07-04 15:48:27)(String), EXCEL(String), oppo_user_template (6).xlsx(String), 9670(Long), 0(Integer), 0(Integer), 0(Integer), PROCESSING(String), SYSTEM(String)
2025-07-04 15:48:28.199 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.insert - <==    Updates: 1
2025-07-04 15:48:28.213 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 创建批次记录: EXCEL_20250704154827_001
2025-07-04 15:48:28.213 [http-nio-10000-exec-1] DEBUG d.o.service.TokenValidationService - Token校验成功
2025-07-04 15:48:28.214 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 开始处理 Excel 文件: oppo_user_template (6).xlsx, 大小: 9670 bytes, 批次ID: EXCEL_20250704154827_001
2025-07-04 15:48:28.789 [http-nio-10000-exec-1] INFO  data.oppodataapi.util.ExcelUtil - 开始读取 Excel 文件: oppo_user_template (6).xlsx, 工作表: 用户数据, 总行数: 2
2025-07-04 15:48:28.794 [http-nio-10000-exec-1] DEBUG data.oppodataapi.util.ExcelUtil - 读取第 2 行数据: douyinId=null, starMapId=7392133155794190373, douyinLink=null
2025-07-04 15:48:28.795 [http-nio-10000-exec-1] INFO  data.oppodataapi.util.ExcelUtil - Excel 文件读取完成，共读取到 1 条有效用户数据
2025-07-04 15:48:28.847 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:48:28.848 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:48:28.887 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:48:28.890 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 周表 oppo_user_2025_27 已存在
2025-07-04 15:48:28.905 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - ==>  Preparing: UPDATE oppo_user_batch SET batch_id=?, batch_name=?, upload_source=?, file_name=?, file_size=?, total_count=?, success_count=?, failed_count=?, upload_status=?, created_by=? WHERE id=?
2025-07-04 15:48:28.906 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - ==> Parameters: EXCEL_20250704154827_001(String), Excel上传 - oppo_user_template (6).xlsx (2025-07-04 15:48:27)(String), EXCEL(String), oppo_user_template (6).xlsx(String), 9670(Long), 1(Integer), 0(Integer), 0(Integer), PROCESSING(String), SYSTEM(String), 33(Long)
2025-07-04 15:48:29.126 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - <==    Updates: 1
2025-07-04 15:48:29.126 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 开始批量上传用户数据到表 oppo_user_2025_27，共 1 条记录，批次ID: EXCEL_20250704154827_001
2025-07-04 15:48:29.126 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 开始分批处理用户数据，总数: 1, 批次大小: 100, 去重模式: 启用 (overwrite=false)
2025-07-04 15:48:29.126 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 处理第 1 批用户数据，共 1 条，批次ID: EXCEL_20250704154827_001, 去重模式: 启用
2025-07-04 15:48:29.127 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 样本数据 - douyinId: null, starMapId: 7392133155794190373, batchId: EXCEL_20250704154827_001, updateTime: 2025-07-04T15:48:29.127260400
2025-07-04 15:48:29.127 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 使用去重模式插入到表: oppo_user_2025_27 (忽略重复数据)
2025-07-04 15:48:29.132 [http-nio-10000-exec-1] DEBUG d.o.m.O.batchInsertOnlyToTable - ==>  Preparing: INSERT IGNORE INTO oppo_user_2025_27 (douyin_id, star_map_id, douyin_link, batch_id, batch_name, upload_source, create_time, update_time, deleted) VALUES ( ?, ?, ?, ?, ?, COALESCE(?, 'MANUAL'), COALESCE(?, NOW()), COALESCE(?, NOW()), COALESCE(?, 0) )
2025-07-04 15:48:29.134 [http-nio-10000-exec-1] DEBUG d.o.m.O.batchInsertOnlyToTable - ==> Parameters: null, 7392133155794190373(String), null, EXCEL_20250704154827_001(String), Excel上传 - oppo_user_template (6).xlsx (2025-07-04 15:48:27)(String), EXCEL(String), 2025-07-04T15:48:28.890289500(LocalDateTime), 2025-07-04T15:48:29.127260400(LocalDateTime), 0(Integer)
2025-07-04 15:48:29.188 [http-nio-10000-exec-1] DEBUG d.o.m.O.batchInsertOnlyToTable - <==    Updates: 0
2025-07-04 15:48:29.188 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 去重模式插入结果: 0 (重复数据已忽略)
2025-07-04 15:48:29.188 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 第 1 批用户数据处理完成，实际处理 0 条（覆盖模式: false），累计成功: 0
2025-07-04 15:48:29.189 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - ==>  Preparing: UPDATE oppo_user_batch SET batch_id=?, batch_name=?, upload_source=?, file_name=?, file_size=?, total_count=?, success_count=?, failed_count=?, upload_status=?, created_by=?, completed_time=? WHERE id=?
2025-07-04 15:48:29.189 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - ==> Parameters: EXCEL_20250704154827_001(String), Excel上传 - oppo_user_template (6).xlsx (2025-07-04 15:48:27)(String), EXCEL(String), oppo_user_template (6).xlsx(String), 9670(Long), 1(Integer), 0(Integer), 0(Integer), SUCCESS(String), SYSTEM(String), 2025-07-04T15:48:29.188250100(LocalDateTime), 33(Long)
2025-07-04 15:48:29.238 [http-nio-10000-exec-1] DEBUG d.o.m.OppoUserBatchMapper.updateById - <==    Updates: 1
2025-07-04 15:48:29.239 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - Excel 文件处理完成: oppo_user_template (6).xlsx, 批次ID: EXCEL_20250704154827_001, 总计: 1, 成功: 0, 失败: 0
2025-07-04 15:48:29.239 [http-nio-10000-exec-1] INFO  d.o.controller.OppoUserController - Excel 文件上传完成，总计: 1, 成功: 0, 失败: 0
2025-07-04 15:49:04.105 [http-nio-10000-exec-3] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 15:49:04.109 [http-nio-10000-exec-3] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 15:49:04.380 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:04.381 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:49:04.492 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:04.494 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 15:49:04.495 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 15:49:04.520 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 15:49:04.520 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:04.521 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 15:49:04.779 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:04.779 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:04.780 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 15:49:05.149 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:05.150 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:05.150 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 15:49:05.513 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:05.514 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:05.514 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 15:49:05.627 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:05.628 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:05.628 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 15:49:05.868 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:05.868 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:05.869 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 15:49:06.046 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.046 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:06.047 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 15:49:06.201 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.201 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:06.201 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 15:49:06.242 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.242 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:06.243 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 15:49:06.344 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.345 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:06.345 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 15:49:06.371 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.372 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:49:06.372 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 15:49:06.554 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:49:06.554 [http-nio-10000-exec-3] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 15:49:06.554 [http-nio-10000-exec-3] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 15:49:06.555 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 15:49:06.555 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 15:49:06.555 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 15:50:03.777 [http-nio-10000-exec-5] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 15:50:03.779 [http-nio-10000-exec-5] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 15:50:03.951 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:03.951 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:50:04.052 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.053 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 15:50:04.053 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 15:50:04.169 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 15:50:04.170 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.170 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 15:50:04.285 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.286 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.286 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 15:50:04.400 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.401 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.402 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 15:50:04.568 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.569 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.569 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 15:50:04.745 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.746 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.746 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 15:50:04.855 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.855 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.856 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 15:50:04.918 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:04.918 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:04.918 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 15:50:05.128 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:05.129 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:05.129 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 15:50:05.277 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:05.278 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:05.278 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 15:50:05.487 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:05.488 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:05.488 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 15:50:05.643 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:05.644 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:05.644 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 15:50:05.757 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:05.758 [http-nio-10000-exec-5] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 15:50:05.758 [http-nio-10000-exec-5] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 15:50:05.758 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 15:50:05.758 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 15:50:05.758 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 15:50:42.702 [http-nio-10000-exec-7] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 15:50:42.704 [http-nio-10000-exec-7] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 15:50:42.727 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:42.728 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:50:42.760 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:42.761 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 15:50:42.761 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 15:50:43.201 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 15:50:43.202 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.202 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 15:50:43.249 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.250 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.250 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 15:50:43.390 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.390 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.391 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 15:50:43.418 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.418 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.418 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 15:50:43.582 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.582 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.582 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 15:50:43.758 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.759 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.759 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 15:50:43.784 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.785 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.785 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 15:50:43.813 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.813 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.813 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 15:50:43.951 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.953 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.953 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 15:50:43.995 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:43.997 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:43.997 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 15:50:44.132 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:44.133 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:50:44.133 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 15:50:44.155 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:50:44.155 [http-nio-10000-exec-7] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 15:51:22.590 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=54s354ms86µs).
2025-07-04 15:51:22.591 [http-nio-10000-exec-7] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 15:51:23.669 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 15:51:23.669 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 15:51:23.669 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 15:51:25.741 [http-nio-10000-exec-9] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 15:51:25.744 [http-nio-10000-exec-9] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 15:51:25.770 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:51:25.771 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:51:25.867 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:52:03.958 [http-nio-10000-exec-9] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 15:52:03.959 [http-nio-10000-exec-9] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 15:52:04.169 [http-nio-10000-exec-9] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 15:52:53.827 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=49s993ms569µs100ns).
2025-07-04 15:52:53.935 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:52:53.937 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 15:52:53.969 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:52:53.971 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:52:53.972 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 15:52:54.001 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:52:54.002 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:52:54.004 [http-nio-10000-exec-9] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 15:52:57.392 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 15:52:57.493 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 14844 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 15:52:57.493 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 15:52:57.494 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 15:52:57.580 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 15:52:57.581 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 15:52:58.698 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 15:52:58.700 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 15:52:58.754 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-04 15:52:59.072 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 15:52:59.968 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 15:52:59.990 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 15:52:59.994 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 15:52:59.994 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 15:53:00.095 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 15:53:00.096 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2513 ms
2025-07-04 15:53:00.397 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 15:53:00.495 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 15:53:01.288 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 15:53:01.386 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 15:53:01.822 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 15:53:01.827 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 15:53:01.827 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 15:53:02.278 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 15:53:02.823 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 15:53:02.823 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 15:53:03.325 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 15:53:03.337 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 15:53:03.352 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 6.78 seconds (process running for 7.805)
2025-07-04 15:53:03.355 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 15:53:03.356 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 15:53:03.356 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 15:53:03.356 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 15:53:03.357 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 15:53:03.357 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 15:53:03.358 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 15:53:03.358 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 15:53:03.358 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 15:56:09.521 [http-nio-10000-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 15:56:09.521 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 15:56:09.523 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 15:56:09.525 [http-nio-10000-exec-1] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 15:56:09.595 [http-nio-10000-exec-1] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 15:56:09.617 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 15:56:10.454 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f4eb001
2025-07-04 15:56:10.456 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 15:56:10.462 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:10.485 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 15:56:10.535 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:14.179 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ?
2025-07-04 15:56:14.181 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 15:56:14.255 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 15:56:19.083 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.085 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 15:56:19.236 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:19.237 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.239 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 15:56:19.449 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:19.451 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.453 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 15:56:19.566 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:19.567 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.569 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 15:56:19.767 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:19.768 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.770 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 15:56:19.929 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:19.931 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:19.932 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 15:56:20.031 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.033 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:20.035 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 15:56:20.150 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.151 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:20.153 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 15:56:20.293 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.295 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:20.296 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 15:56:20.488 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.490 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:20.491 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 15:56:20.517 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.519 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 15:56:20.521 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 15:56:20.651 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 15:56:20.652 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 16:03:03.805 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m49s848ms211µs100ns).
2025-07-04 16:03:04.674 [http-nio-10000-exec-1] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 16:03:06.736 [scheduling-1] INFO  d.oppodataapi.task.TokenRefreshTask - 开始执行定时Token检查和刷新任务
2025-07-04 16:03:06.737 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 16:03:06.739 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 16:03:06.740 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:03:07.077 [scheduling-1] DEBUG d.o.m.A.selectExpiringTokens - ==>  Preparing: SELECT * FROM api_token WHERE is_active = 1 AND expires_at IS NOT NULL AND expires_at BETWEEN NOW() AND ? ORDER BY expires_at ASC
2025-07-04 16:03:07.242 [scheduling-1] DEBUG d.o.m.A.selectExpiringTokens - ==> Parameters: 2025-07-04T17:03:06.737396(LocalDateTime)
2025-07-04 16:03:09.010 [scheduling-1] DEBUG d.o.m.A.selectExpiringTokens - <==      Total: 0
2025-07-04 16:03:09.010 [scheduling-1] INFO  d.oppodataapi.task.TokenRefreshTask - 定时Token检查和刷新任务执行完成
2025-07-04 16:03:33.018 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-04 16:03:33.021 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-04 16:15:46.770 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 16:15:46.863 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 26332 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 16:15:46.864 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 16:15:46.865 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 16:15:46.967 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 16:15:46.967 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 16:15:48.165 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 16:15:48.169 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 16:15:48.226 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-07-04 16:15:48.563 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 16:15:49.532 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 16:15:49.556 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 16:15:49.560 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 16:15:49.561 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 16:15:49.650 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 16:15:49.652 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2682 ms
2025-07-04 16:15:50.043 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 16:15:50.158 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 16:15:51.028 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 16:15:51.111 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 16:15:51.649 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 16:15:51.653 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 16:15:51.654 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 16:15:52.127 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 16:15:52.863 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 16:15:52.864 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 16:15:53.467 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 16:15:53.479 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 16:15:53.498 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.614 seconds (process running for 8.839)
2025-07-04 16:15:53.502 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 16:15:53.502 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 16:15:53.502 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 16:15:53.502 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 16:15:53.505 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 16:15:53.505 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 16:15:53.505 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 16:15:53.505 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 16:15:53.507 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 16:17:33.821 [http-nio-10000-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 16:17:33.822 [http-nio-10000-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 16:17:33.823 [http-nio-10000-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 16:17:33.827 [http-nio-10000-exec-2] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:17:33.914 [http-nio-10000-exec-2] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:17:33.942 [http-nio-10000-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 16:17:34.771 [http-nio-10000-exec-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@71cb5636
2025-07-04 16:17:34.774 [http-nio-10000-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 16:17:34.790 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:34.836 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:17:34.986 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:39.789 [http-nio-10000-exec-2] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:17:39.791 [http-nio-10000-exec-2] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 16:17:39.894 [http-nio-10000-exec-2] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 16:17:43.725 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:43.727 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:17:43.817 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:43.818 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:43.820 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:17:43.846 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:43.847 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:43.849 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:17:43.889 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:43.891 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:43.892 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:17:44.129 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.131 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.133 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:17:44.186 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.188 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.190 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:17:44.218 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.220 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.221 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:17:44.263 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.264 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.266 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:17:44.334 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.336 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.338 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:17:44.547 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.550 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.551 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:17:44.613 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.616 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:17:44.618 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:17:44.735 [http-nio-10000-exec-2] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:17:44.736 [http-nio-10000-exec-2] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 16:17:44.736 [http-nio-10000-exec-2] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 16:17:44.737 [http-nio-10000-exec-2] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 16:17:44.737 [http-nio-10000-exec-2] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 16:17:44.737 [http-nio-10000-exec-2] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:18:21.304 [http-nio-10000-exec-3] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:18:21.307 [http-nio-10000-exec-3] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:18:41.369 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:18:41.370 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:18:53.705 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 16:18:53.825 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 23916 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 16:18:53.826 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 16:18:53.827 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 16:18:53.933 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 16:18:53.934 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 16:18:55.042 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 16:18:55.046 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 16:18:55.102 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-04 16:18:55.426 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 16:18:56.427 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 16:18:56.451 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 16:18:56.455 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 16:18:56.455 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 16:18:56.540 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 16:18:56.541 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2606 ms
2025-07-04 16:18:56.897 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 16:18:57.021 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 16:18:57.903 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 16:18:57.986 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 16:18:58.464 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 16:18:58.473 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 16:18:58.474 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 16:18:58.951 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 16:18:59.572 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 16:18:59.573 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 16:19:00.203 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 16:19:00.225 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 16:19:00.252 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.381 seconds (process running for 8.315)
2025-07-04 16:19:00.256 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 16:19:00.256 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 16:19:00.256 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 16:19:00.256 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 16:19:00.258 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 16:19:00.258 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 16:19:00.258 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 16:19:00.258 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 16:19:00.259 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 16:19:00.259 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 16:19:00.259 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 16:19:00.259 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 16:19:00.259 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 16:19:00.259 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 16:19:00.260 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 16:19:54.553 [http-nio-10000-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 16:19:54.554 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 16:19:54.555 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 16:19:54.558 [http-nio-10000-exec-1] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:19:59.791 [http-nio-10000-exec-1] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:19:59.814 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 16:20:00.630 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@720c040e
2025-07-04 16:20:00.633 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 16:20:00.641 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:00.665 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:20:00.905 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:29.245 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:20:29.246 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 16:20:29.306 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 16:20:29.307 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:29.307 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:20:29.571 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:29.571 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:29.572 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:20:29.663 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:29.664 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:29.664 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:20:29.690 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:29.690 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:29.690 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:20:29.919 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:29.920 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:29.920 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:20:30.009 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.009 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.010 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:20:30.043 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.043 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.044 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:20:30.212 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.213 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.213 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:20:30.267 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.268 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.268 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:20:30.378 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.379 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.379 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:20:30.404 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.405 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:20:30.405 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:20:30.594 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:20:30.595 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 0 个用户
2025-07-04 16:20:42.219 [http-nio-10000-exec-1] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 中没有找到用户数据
2025-07-04 16:20:42.220 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 16:20:42.220 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 16:20:42.220 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:23:57.253 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-04 16:23:57.255 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-04 16:27:29.158 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-04 16:27:29.261 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Starting OppoDataApiApplication using Java 21.0.6 with PID 14836 (C:\20240218\work\oppo-data-api\target\classes started by qianchuan in C:\20240218\work\oppo-data-api)
2025-07-04 16:27:29.262 [restartedMain] DEBUG d.oppodataapi.OppoDataApiApplication - Running with Spring Boot v3.3.8, Spring v6.1.16
2025-07-04 16:27:29.263 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - The following 1 profile is active: "develop"
2025-07-04 16:27:29.380 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 16:27:29.381 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 16:27:30.680 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 16:27:30.683 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 16:27:30.737 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-07-04 16:27:31.089 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7f0fe389-7841-3e17-8e73-4e2a15db108d
2025-07-04 16:27:32.053 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 10000 (http)
2025-07-04 16:27:32.074 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10000"]
2025-07-04 16:27:32.079 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 16:27:32.079 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-04 16:27:32.173 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 16:27:32.175 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2792 ms
2025-07-04 16:27:32.522 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> start !
2025-07-04 16:27:32.634 [restartedMain] INFO  c.t.u.web.common.RedisConfig - redisTemplate init ----> end !
2025-07-04 16:27:33.470 [restartedMain] DEBUG data.oppodataapi.global.TokenFilter - Filter 'tokenFilter' configured for use
2025-07-04 16:27:33.538 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-04 16:27:34.025 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[data.oppodataapi.mapper.OppoDataMapper.selectByUserId] is ignored, because it exists, maybe from xml file
2025-07-04 16:27:34.031 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "data.oppodataapi.entity.OppoData".
2025-07-04 16:27:34.032 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class data.oppodataapi.entity.OppoData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-04 16:27:34.478 [restartedMain] WARN  d.o.config.GracefulStartupConfig - UserCenterService不可用，使用默认占位符实现
2025-07-04 16:27:35.126 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Found 2 AuthenticationProvider beans, with names [daoAuthenticationProvider, emailAuthenticationProvider]. Global Authentication Manager will not be configured with AuthenticationProviders. Consider publishing a single AuthenticationProvider bean, or wiring your Providers directly using the DSL.
2025-07-04 16:27:35.129 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name jwtUserDetailsService
2025-07-04 16:27:35.735 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10000"]
2025-07-04 16:27:35.748 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 10000 (http) with context path '/'
2025-07-04 16:27:35.766 [restartedMain] INFO  d.oppodataapi.OppoDataApiApplication - Started OppoDataApiApplication in 7.575 seconds (process running for 8.741)
2025-07-04 16:27:35.771 [restartedMain] INFO  d.o.config.StartupValidator - 开始启动验证...
2025-07-04 16:27:35.772 [restartedMain] INFO  d.o.config.StartupValidator - 检查关键类...
2025-07-04 16:27:35.772 [restartedMain] INFO  d.o.config.StartupValidator - ✓ MyBatis类可用
2025-07-04 16:27:35.772 [restartedMain] INFO  d.o.config.StartupValidator - ✓ UserCenterService类可用
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - ✓ Apache POI类可用
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - 检查数据库连接...
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - 数据库连接检查跳过（将在后续初始化时验证）
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - 检查MyBatis配置...
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - Mapper扫描路径: data.oppodataapi.mapper
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - MyBatis配置检查完成
2025-07-04 16:27:35.773 [restartedMain] INFO  d.o.config.StartupValidator - 启动验证完成，所有关键组件正常
2025-07-04 16:27:35.774 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - 开始验证Quark配置...
2025-07-04 16:27:35.774 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator - Quark配置验证成功:
2025-07-04 16:27:35.774 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 用户名: op****@tarsocial.com
2025-07-04 16:27:35.775 [restartedMain] INFO  d.oppodataapi.config.ConfigValidator -   - 密码: 30****5@
2025-07-04 16:27:42.960 [http-nio-10000-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 16:27:42.960 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 16:27:42.962 [http-nio-10000-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-04 16:27:42.965 [http-nio-10000-exec-1] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:27:46.522 [http-nio-10000-exec-1] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:27:46.547 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-04 16:27:47.326 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4b967d27
2025-07-04 16:27:47.329 [http-nio-10000-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-04 16:27:47.336 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:47.364 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:27:47.640 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:50.851 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:27:50.851 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 16:27:51.010 [http-nio-10000-exec-1] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 1
2025-07-04 16:27:51.011 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 从表 oppo_user_2025_27 查询到批次 EXCEL_20250704154827_001 的 1 个用户
2025-07-04 16:27:51.011 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.012 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:27:51.211 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.212 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.212 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:27:51.235 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.235 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.236 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:27:51.332 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.333 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.333 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:27:51.359 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.360 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.360 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:27:51.594 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.595 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.595 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:27:51.681 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.681 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.682 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:27:51.708 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.709 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.710 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:27:51.826 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.827 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.827 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:27:51.949 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:51.949 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:51.950 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:27:52.150 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:52.151 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:27:52.152 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:27:52.271 [http-nio-10000-exec-1] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:27:52.272 [http-nio-10000-exec-1] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 1 个用户
2025-07-04 16:27:56.267 [http-nio-10000-exec-1] INFO  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共提取到 2 个唯一用户ID
2025-07-04 16:27:56.267 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 2, 批次大小: 100
2025-07-04 16:27:56.267 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 总批次数: 1
2025-07-04 16:27:56.267 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 处理第 1/1 批次，用户ID数量: 2 (索引 0 到 1)
2025-07-04 16:27:56.267 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 开始查询批次 1 的数据，用户ID数量: 2
2025-07-04 16:27:56.314 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - ==>  Preparing: SELECT * FROM api_token WHERE token_name = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
2025-07-04 16:27:56.315 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:27:56.352 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - <==      Total: 1
2025-07-04 16:27:56.354 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - ==>  Preparing: UPDATE api_token SET last_used_at = NOW(), usage_count = usage_count + 1, updated_at = NOW() WHERE token_name = ?
2025-07-04 16:27:56.354 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:27:57.029 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - <==    Updates: 1
2025-07-04 16:27:57.030 [http-nio-10000-exec-1] DEBUG d.oppodataapi.service.TokenService - 从数据库获取到有效Token: eyJ0****Xy7A
2025-07-04 16:27:57.030 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 调用文档源查询接口: https://dag-api.tarsocial.com/dc/extend/doc-source
2025-07-04 16:27:57.030 [http-nio-10000-exec-1] DEBUG d.o.service.OppoDataService - 请求参数: DocSourceRequest(top=10, platformIds=[44], extraCondition=[{fieldId=446, operator=0, value=[yizhiyang0115, 7392133155794190373]}], indexType=user, mainPartyId=[341497], rowVo=[], columnVo=null, metricsVo=[], path=oppo, title=总计, downloadCount=[0], flatSource=true, pageSize=100, pageNum=1, arrayFlatSource=true, highLight=true, token=null, startDate=null, endDate=null)
2025-07-04 16:27:58.684 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 请求ID: a2fdf8eefd2d4c1dbcfec40100771d6d.103.17516176772338917
2025-07-04 16:27:58.685 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 文档源查询接口调用成功
2025-07-04 16:28:32.204 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - ==>  Preparing: SELECT * FROM api_token WHERE token_name = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
2025-07-04 16:28:32.204 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:28:32.340 [http-nio-10000-exec-1] DEBUG d.o.m.A.selectValidTokenByName - <==      Total: 1
2025-07-04 16:28:32.340 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - ==>  Preparing: UPDATE api_token SET last_used_at = NOW(), usage_count = usage_count + 1, updated_at = NOW() WHERE token_name = ?
2025-07-04 16:28:32.340 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:28:32.691 [http-nio-10000-exec-1] DEBUG d.o.m.A.updateTokenUsage - <==    Updates: 1
2025-07-04 16:28:32.692 [http-nio-10000-exec-1] DEBUG d.oppodataapi.service.TokenService - 从数据库获取到有效Token: eyJ0****Xy7A
2025-07-04 16:28:32.692 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 调用文档源查询接口: https://dag-api.tarsocial.com/dc/extend/doc-source
2025-07-04 16:28:32.693 [http-nio-10000-exec-1] DEBUG d.o.service.OppoDataService - 请求参数: DocSourceRequest(top=10, platformIds=[44], extraCondition=[{fieldId=267, operator=4, value=[2024-07-04 16:28:32]}, {fieldId=267, operator=6, value=[2025-07-04 16:28:32], allowNull=0}, {fieldId=220, operator=0, value=[yizhiyang0115, 7392133155794190373]}, {fieldId=312, operator=9, value=[评论], allowNull=1}], indexType=post, mainPartyId=[343907], rowVo=[], columnVo=null, metricsVo=[], path=oppo-post, title=总计, downloadCount=[2], flatSource=true, pageSize=100, pageNum=1, arrayFlatSource=true, highLight=true, token=null, startDate=2025-01-04T16:28:32.086Z, endDate=2025-07-04T16:28:32.086Z)
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 请求ID: a2fdf8eefd2d4c1dbcfec40100771d6d.1338422.17516177123739847
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 文档源查询接口调用成功
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 批次 1 第 1 页没有数据，查询结束
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 批次 1 查询完成，总共获取 0 条数据
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 批次 1/1 处理成功，获取数据: 0 条
2025-07-04 16:28:34.734 [http-nio-10000-exec-1] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:30:10.490 [http-nio-10000-exec-3] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:30:10.494 [http-nio-10000-exec-3] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:30:10.566 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:10.567 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:30:10.596 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:14.886 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:30:14.886 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250704154827_001(String)
2025-07-04 16:30:15.715 [http-nio-10000-exec-3] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 1
2025-07-04 16:30:15.715 [http-nio-10000-exec-3] DEBUG d.o.service.OppoUserService - 从表 oppo_user_2025_27 查询到批次 EXCEL_20250704154827_001 的 1 个用户
2025-07-04 16:30:15.715 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:15.716 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:30:15.793 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:15.793 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:15.794 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:30:15.821 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:15.822 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:15.823 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:30:15.995 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:15.995 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:15.996 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:30:16.022 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.023 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.023 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:30:16.052 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.052 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.053 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:30:16.324 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.324 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.324 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:30:16.349 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.350 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.350 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:30:16.381 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.382 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.382 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:30:16.408 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.409 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.409 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:30:16.433 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.434 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:30:16.434 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:30:16.685 [http-nio-10000-exec-3] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:30:16.685 [http-nio-10000-exec-3] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共查询到 1 个用户
2025-07-04 16:30:16.685 [http-nio-10000-exec-3] INFO  d.o.service.OppoUserService - 批次 EXCEL_20250704154827_001 共提取到 2 个唯一用户ID
2025-07-04 16:33:04.809 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m2s706ms904µs200ns).
2025-07-04 16:33:04.812 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 2, 批次大小: 100
2025-07-04 16:33:04.813 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 总批次数: 1
2025-07-04 16:33:04.813 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 处理第 1/1 批次，用户ID数量: 2 (索引 0 到 1)
2025-07-04 16:33:04.814 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 开始查询批次 1 的数据，用户ID数量: 2
2025-07-04 16:33:04.968 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - ==>  Preparing: SELECT * FROM api_token WHERE token_name = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
2025-07-04 16:33:04.970 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:33:05.009 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - <==      Total: 1
2025-07-04 16:33:05.011 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - ==>  Preparing: UPDATE api_token SET last_used_at = NOW(), usage_count = usage_count + 1, updated_at = NOW() WHERE token_name = ?
2025-07-04 16:33:05.013 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:33:05.372 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - <==    Updates: 1
2025-07-04 16:33:05.373 [http-nio-10000-exec-3] DEBUG d.oppodataapi.service.TokenService - 从数据库获取到有效Token: eyJ0****Xy7A
2025-07-04 16:33:05.374 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 调用文档源查询接口: https://dag-api.tarsocial.com/dc/extend/doc-source
2025-07-04 16:33:05.374 [http-nio-10000-exec-3] DEBUG d.o.service.OppoDataService - 请求参数: DocSourceRequest(top=10, platformIds=[44], extraCondition=[{fieldId=446, operator=0, value=[yizhiyang0115, 7392133155794190373]}], indexType=user, mainPartyId=[341497], rowVo=[], columnVo=null, metricsVo=[], path=oppo, title=总计, downloadCount=[0], flatSource=true, pageSize=100, pageNum=1, arrayFlatSource=true, highLight=true, token=null, startDate=null, endDate=null)
2025-07-04 16:33:06.671 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 请求ID: a2fdf8eefd2d4c1dbcfec40100771d6d.97.17516179851931211
2025-07-04 16:33:06.671 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 文档源查询接口调用成功
2025-07-04 16:33:30.025 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - ==>  Preparing: SELECT * FROM api_token WHERE token_name = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
2025-07-04 16:33:30.027 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:33:30.157 [http-nio-10000-exec-3] DEBUG d.o.m.A.selectValidTokenByName - <==      Total: 1
2025-07-04 16:33:30.159 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - ==>  Preparing: UPDATE api_token SET last_used_at = NOW(), usage_count = usage_count + 1, updated_at = NOW() WHERE token_name = ?
2025-07-04 16:33:30.161 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:33:30.205 [http-nio-10000-exec-3] DEBUG d.o.m.A.updateTokenUsage - <==    Updates: 1
2025-07-04 16:33:30.206 [http-nio-10000-exec-3] DEBUG d.oppodataapi.service.TokenService - 从数据库获取到有效Token: eyJ0****Xy7A
2025-07-04 16:33:30.206 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 调用文档源查询接口: https://dag-api.tarsocial.com/dc/extend/doc-source
2025-07-04 16:33:30.206 [http-nio-10000-exec-3] DEBUG d.o.service.OppoDataService - 请求参数: DocSourceRequest(top=10, platformIds=[44], extraCondition=[{fieldId=267, operator=4, value=[2024-07-04 16:33:29]}, {fieldId=267, operator=6, value=[2025-07-04 16:33:29], allowNull=0}, {fieldId=220, operator=0, value=[yizhiyang0115, 7392133155794190373]}, {fieldId=312, operator=9, value=[评论], allowNull=1}], indexType=post, mainPartyId=[343907], rowVo=[], columnVo=null, metricsVo=[], path=oppo-post, title=总计, downloadCount=[2], flatSource=true, pageSize=100, pageNum=1, arrayFlatSource=true, highLight=true, token=null, startDate=2025-01-04T16:33:29.850Z, endDate=2025-07-04T16:33:29.850Z)
2025-07-04 16:33:32.050 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 请求ID: 2d8166c504554d6e9977972f16ab0dd2.105.17516180097977525
2025-07-04 16:33:32.051 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 文档源查询接口调用成功
2025-07-04 16:33:32.051 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 批次 1 第 1 页没有数据，查询结束
2025-07-04 16:33:32.051 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 批次 1 查询完成，总共获取 0 条数据
2025-07-04 16:33:32.052 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 批次 1/1 处理成功，获取数据: 0 条
2025-07-04 16:33:32.052 [http-nio-10000-exec-3] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:34:33.452 [http-nio-10000-exec-5] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:34:33.456 [http-nio-10000-exec-5] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:34:34.328 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.329 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:34:34.410 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.411 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:34:34.411 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250703182120_001(String)
2025-07-04 16:34:34.469 [http-nio-10000-exec-5] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 0
2025-07-04 16:34:34.470 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.471 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:34:34.504 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.505 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.505 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:34:34.553 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.554 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.554 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:34:34.699 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.700 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.700 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:34:34.846 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.846 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.846 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:34:34.871 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.872 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.872 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:34:34.971 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:34.972 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:34.972 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:34:35.422 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:35.423 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:35.423 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:34:35.522 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:35.522 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:35.522 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:34:35.556 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:35.557 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:35.557 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:34:35.602 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:35.603 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:34:35.603 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:34:35.708 [http-nio-10000-exec-5] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:34:35.708 [http-nio-10000-exec-5] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250703182120_001 共查询到 0 个用户
2025-07-04 16:34:35.708 [http-nio-10000-exec-5] WARN  d.o.service.OppoUserService - 批次 EXCEL_20250703182120_001 中没有找到用户数据
2025-07-04 16:34:42.343 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 0, 批次大小: 100
2025-07-04 16:34:42.343 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 总批次数: 0
2025-07-04 16:34:42.344 [http-nio-10000-exec-5] INFO  d.o.service.OppoDataService - 所有批次处理完成，总获取数据: 0 条
2025-07-04 16:35:21.716 [http-nio-10000-exec-7] INFO  data.oppodataapi.global.TokenFilter - contentType -> null, uri->/api/oppo/run-data-by-batch 
2025-07-04 16:35:21.719 [http-nio-10000-exec-7] DEBUG d.o.util.UserWeeklyTableUtil - 获取最近12周的表名: [oppo_user_2025_27, oppo_user_2025_26, oppo_user_2025_25, oppo_user_2025_24, oppo_user_2025_23, oppo_user_2025_22, oppo_user_2025_21, oppo_user_2025_20, oppo_user_2025_19, oppo_user_2025_18, oppo_user_2025_17, oppo_user_2025_16]
2025-07-04 16:35:21.756 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:21.757 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_27(String)
2025-07-04 16:35:21.780 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:21.781 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - ==>  Preparing: SELECT * FROM oppo_user_2025_27 WHERE batch_id = ? AND deleted = 0
2025-07-04 16:35:21.781 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - ==> Parameters: EXCEL_20250703172544_001(String)
2025-07-04 16:35:24.175 [http-nio-10000-exec-7] DEBUG d.o.m.O.selectByBatchIdFromTable - <==      Total: 1174
2025-07-04 16:35:24.175 [http-nio-10000-exec-7] DEBUG d.o.service.OppoUserService - 从表 oppo_user_2025_27 查询到批次 EXCEL_20250703172544_001 的 1174 个用户
2025-07-04 16:35:24.176 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.176 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_26(String)
2025-07-04 16:35:24.272 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.273 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.273 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_25(String)
2025-07-04 16:35:24.297 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.298 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.298 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_24(String)
2025-07-04 16:35:24.338 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.338 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.339 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_23(String)
2025-07-04 16:35:24.577 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.578 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.579 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_22(String)
2025-07-04 16:35:24.632 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.633 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.633 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_21(String)
2025-07-04 16:35:24.671 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.672 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.672 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_20(String)
2025-07-04 16:35:24.696 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.697 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.697 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_19(String)
2025-07-04 16:35:24.730 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.730 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.731 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_18(String)
2025-07-04 16:35:24.969 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:24.970 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:24.970 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_17(String)
2025-07-04 16:35:25.006 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:25.007 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==>  Preparing: SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?
2025-07-04 16:35:25.007 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - ==> Parameters: oppo_user_2025_16(String)
2025-07-04 16:35:25.039 [http-nio-10000-exec-7] DEBUG d.o.m.O.checkTableExists - <==      Total: 1
2025-07-04 16:35:25.039 [http-nio-10000-exec-7] DEBUG d.o.service.OppoUserService - 批次 EXCEL_20250703172544_001 共查询到 1174 个用户
2025-07-04 16:35:25.040 [http-nio-10000-exec-7] INFO  d.o.service.OppoUserService - 批次 EXCEL_20250703172544_001 共提取到 2135 个唯一用户ID
2025-07-04 16:35:31.632 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 开始处理用户数据查询，总用户数: 2135, 批次大小: 100
2025-07-04 16:35:31.633 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 总批次数: 22
2025-07-04 16:35:31.633 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 处理第 1/22 批次，用户ID数量: 100 (索引 0 到 99)
2025-07-04 16:35:31.634 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 开始查询批次 1 的数据，用户ID数量: 100
2025-07-04 16:35:31.708 [http-nio-10000-exec-7] DEBUG d.o.m.A.selectValidTokenByName - ==>  Preparing: SELECT * FROM api_token WHERE token_name = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
2025-07-04 16:35:31.710 [http-nio-10000-exec-7] DEBUG d.o.m.A.selectValidTokenByName - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:35:31.952 [http-nio-10000-exec-7] DEBUG d.o.m.A.selectValidTokenByName - <==      Total: 1
2025-07-04 16:35:31.953 [http-nio-10000-exec-7] DEBUG d.o.m.A.updateTokenUsage - ==>  Preparing: UPDATE api_token SET last_used_at = NOW(), usage_count = usage_count + 1, updated_at = NOW() WHERE token_name = ?
2025-07-04 16:35:31.955 [http-nio-10000-exec-7] DEBUG d.o.m.A.updateTokenUsage - ==> Parameters: datacenter_api_token(String)
2025-07-04 16:35:32.093 [http-nio-10000-exec-7] DEBUG d.o.m.A.updateTokenUsage - <==    Updates: 1
2025-07-04 16:35:32.094 [http-nio-10000-exec-7] DEBUG d.oppodataapi.service.TokenService - 从数据库获取到有效Token: eyJ0****Xy7A
2025-07-04 16:35:32.094 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 调用文档源查询接口: https://dag-api.tarsocial.com/dc/extend/doc-source
2025-07-04 16:35:32.095 [http-nio-10000-exec-7] DEBUG d.o.service.OppoDataService - 请求参数: DocSourceRequest(top=10, platformIds=[44], extraCondition=[{fieldId=446, operator=0, value=[dyick9ix4pfs, 6825910586467942407, Hhw.20050312, 7163217408918978597, xjw01150936, 7377210286350008372, fxqdzxzq123, wzl46501703, 7219873349868978209, rowenwong1234567, 161105999, 7162489180453339172, 1832130512, 6853269339181678606, 46554694994, 7169439391947948046, kejijijie, 7491149121789640742, TechOtakus, 6967724798659002382, YFlowerY, 7210278600359018553, drincool, 6834032454001491982, xst13145858, 7396934877934944294, 86632646119, 7443369838862401555, yudufeifan000, 0, 87409518562, 7373611744591085622, 42650793954, 69670203022, 7051609731504275470, xiaoxiao52004, 7127921691883208712, 72008842900, 7392526135570989067, shake_ye2, 6712583168014155783, JXBG.., 7239288256286228538, 1105297476, low89038, 7360354557156655145, 1585761521, 992484354, 6698546964377632775, NFYY., 7270831336384888887, 128567096, lipuchen, 7039962340644634632, 94885392630, 7306733237706063911, zhangyuwan2333, 72600738296, 6870168325855379470, Mi_ZiYao, 6927594153605857294, 15109666, 6705196699284930567, 95100615, 22lsh020706, 6972572818995167262, CSHT0515, 7063288185530351623, jinyuzly52, 7067449628156919823, houlai00520, 7033386851113107470, yubie_carplay, 6737528677187190788, 286427, 7175524592906141729, 973932527, 6745714542963064846, HANGZIART, 6914086978237497351, L_0928ann, 7187089091219947575, 4175696, 6596680013926367239, Atong18888, 6870171678236737544, 94085182911, 7162094708234125324, 956506724, 7136797131746050061, haiwenwanji6, 75924677147, 7214864254157979706, Jia2oo511o1, 7179078774971957309, hongfs67842, 6668817195004329988, DuKY0126, GJX17630068175, 6870171564877283341]}], indexType=user, mainPartyId=[341497], rowVo=[], columnVo=null, metricsVo=[], path=oppo, title=总计, downloadCount=[0], flatSource=true, pageSize=100, pageNum=1, arrayFlatSource=true, highLight=true, token=null, startDate=null, endDate=null)
2025-07-04 16:35:33.952 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 请求ID: a2fdf8eefd2d4c1dbcfec40100771d6d.819710.17516181319396033
2025-07-04 16:35:33.952 [http-nio-10000-exec-7] INFO  d.o.service.OppoDataService - 文档源查询接口调用成功
2025-07-04 16:36:32.964 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m28s141ms194µs400ns).
2025-07-04 16:43:47.585 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m14s621ms893µs200ns).
