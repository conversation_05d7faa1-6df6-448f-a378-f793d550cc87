# 测试品牌逻辑修复功能

### 1. 测试正常情况（既有商业合作品牌也有3C品牌）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5
}

###

### 2. 测试只有3C品牌的情况（商业合作品牌为空）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}

###

### 3. 测试只有商业合作品牌的情况（3C品牌为空）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "starMapId": "6857515126107406344"
    }
  ]
}

###

### 4. 测试多个用户的品牌信息
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 10,
  "userCondition": [
    {
      "douyinId": "68370542454"
    },
    {
      "starMapId": "6857515126107406344"
    },
    {
      "userId": "user_123456"
    }
  ]
}

###

### 5. 查看系统诊断信息（检查品牌处理逻辑）
GET http://localhost:10000/api/oppo/diagnose

###

### 6. 调试用户查询条件（验证品牌字段）
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": [
    {
      "douyinId": "68370542454"
    }
  ]
}

###

### 7. 测试健康检查
GET http://localhost:10000/api/oppo/health

###

### 8. 查看最新表信息
GET http://localhost:10000/api/oppo/latest-table
