package data.oppodataapi.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * kw_brand 字段处理测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class KwBrandProcessingTest {

    /**
     * 测试处理不同类型的 kw_brand 字段
     */
    @Test
    public void testProcessKwBrand() {
        // 测试用例1：String 类型
        Map<String, Object> sourceMap1 = new HashMap<>();
        sourceMap1.put("kw_brand", "iPhone手机很好用");
        String result1 = processKwBrand(sourceMap1);
        assertEquals("iPhone手机很好用", result1);
        
        // 测试用例2：ArrayList 类型
        Map<String, Object> sourceMap2 = new HashMap<>();
        List<String> brandList = Arrays.asList("iPhone", "小米", "OPPO");
        sourceMap2.put("kw_brand", brandList);
        String result2 = processKwBrand(sourceMap2);
        assertEquals("iPhone,小米,OPPO", result2);
        
        // 测试用例3：包含null元素的ArrayList
        Map<String, Object> sourceMap3 = new HashMap<>();
        List<Object> brandListWithNull = Arrays.asList("iPhone", null, "小米", "", "OPPO");
        sourceMap3.put("kw_brand", brandListWithNull);
        String result3 = processKwBrand(sourceMap3);
        assertEquals("iPhone,小米,,OPPO", result3);
        
        // 测试用例4：空的ArrayList
        Map<String, Object> sourceMap4 = new HashMap<>();
        sourceMap4.put("kw_brand", new ArrayList<>());
        String result4 = processKwBrand(sourceMap4);
        assertNull(result4);
        
        // 测试用例5：null值
        Map<String, Object> sourceMap5 = new HashMap<>();
        sourceMap5.put("kw_brand", null);
        String result5 = processKwBrand(sourceMap5);
        assertNull(result5);
        
        // 测试用例6：其他类型（Integer）
        Map<String, Object> sourceMap6 = new HashMap<>();
        sourceMap6.put("kw_brand", 12345);
        String result6 = processKwBrand(sourceMap6);
        assertEquals("12345", result6);
        
        // 测试用例7：空字符串
        Map<String, Object> sourceMap7 = new HashMap<>();
        sourceMap7.put("kw_brand", "");
        String result7 = processKwBrand(sourceMap7);
        assertEquals("", result7);
        
        // 测试用例8：只包含空格的字符串
        Map<String, Object> sourceMap8 = new HashMap<>();
        sourceMap8.put("kw_brand", "   ");
        String result8 = processKwBrand(sourceMap8);
        assertEquals("   ", result8);
    }
    
    /**
     * 模拟处理 kw_brand 字段的逻辑
     * 这里复制了实际的实现逻辑用于测试
     */
    private String processKwBrand(Map<String, Object> sourceMap) {
        Object kwBrandObj = sourceMap.get("kw_brand");
        String kwBrand = null;
        
        if (kwBrandObj == null) {
            return null;
        } else if (kwBrandObj instanceof String) {
            // 如果是字符串类型
            kwBrand = (String) kwBrandObj;
        } else if (kwBrandObj instanceof java.util.List) {
            // 如果是列表类型，将列表元素用逗号连接
            @SuppressWarnings("unchecked")
            java.util.List<Object> kwBrandList = (java.util.List<Object>) kwBrandObj;
            if (!kwBrandList.isEmpty()) {
                kwBrand = kwBrandList.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            }
        } else {
            // 其他类型，转换为字符串
            kwBrand = kwBrandObj.toString();
        }
        
        return kwBrand;
    }
    
    /**
     * 测试3C品牌提取（结合kw_brand处理）
     */
    @Test
    public void testExtractThreeCBrandsWithDifferentTypes() {
        // 测试用例1：String类型包含3C品牌
        Map<String, Object> sourceMap1 = new HashMap<>();
        sourceMap1.put("kw_brand", "我使用iPhone和小米手机");
        String kwBrand1 = processKwBrand(sourceMap1);
        String result1 = extractThreeCBrands(kwBrand1);
        assertEquals("iPhone,小米", result1);
        
        // 测试用例2：ArrayList类型包含3C品牌
        Map<String, Object> sourceMap2 = new HashMap<>();
        List<String> brandList = Arrays.asList("iPhone手机", "小米电视", "OPPO拍照");
        sourceMap2.put("kw_brand", brandList);
        String kwBrand2 = processKwBrand(sourceMap2);
        String result2 = extractThreeCBrands(kwBrand2);
        assertEquals("iPhone,小米,OPPO", result2);
        
        // 测试用例3：混合类型
        Map<String, Object> sourceMap3 = new HashMap<>();
        List<Object> mixedList = Arrays.asList("华为手机", 123, "Samsung电视", null, "苹果电脑");
        sourceMap3.put("kw_brand", mixedList);
        String kwBrand3 = processKwBrand(sourceMap3);
        String result3 = extractThreeCBrands(kwBrand3);
        assertEquals("华为,Samsung,苹果", result3);
    }
    
    /**
     * 模拟3C品牌提取逻辑
     */
    private String extractThreeCBrands(String kwBrand) {
        if (kwBrand == null || kwBrand.trim().isEmpty()) {
            return "";
        }
        
        // 定义3C品牌关键词（不区分大小写）
        String[] threeCBrandKeywords = {
            "vivo", "小米", "xiaomi", "OPPO", "华为", "huawei", 
            "三星", "Samsung", "红米", "redmi", "一加", "oneplus", 
            "荣耀", "HONOR", "苹果", "iPhone", "Apple"
        };
        
        // 使用LinkedHashSet保持顺序并去重
        Set<String> matchedBrands = new LinkedHashSet<>();
        
        // 将kwBrand转换为小写进行匹配
        String kwBrandLower = kwBrand.toLowerCase();
        
        // 遍历所有3C品牌关键词
        for (String keyword : threeCBrandKeywords) {
            String keywordLower = keyword.toLowerCase();
            
            // 检查是否包含该关键词
            if (kwBrandLower.contains(keywordLower)) {
                // 添加原始关键词（保持原有大小写）
                matchedBrands.add(keyword);
            }
        }
        
        // 将匹配的品牌用逗号连接
        return String.join(",", matchedBrands);
    }
    
    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        // 测试非常大的ArrayList
        Map<String, Object> sourceMap = new HashMap<>();
        List<String> largeBrandList = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            largeBrandList.add("brand" + i);
        }
        largeBrandList.add("iPhone");
        largeBrandList.add("小米");
        
        sourceMap.put("kw_brand", largeBrandList);
        String kwBrand = processKwBrand(sourceMap);
        String result = extractThreeCBrands(kwBrand);
        
        assertTrue(result.contains("iPhone"));
        assertTrue(result.contains("小米"));
    }
    
    /**
     * 测试性能
     */
    @Test
    public void testPerformance() {
        Map<String, Object> sourceMap = new HashMap<>();
        List<String> brandList = new ArrayList<>();
        
        // 创建包含1000个元素的列表
        for (int i = 0; i < 1000; i++) {
            brandList.add("brand" + i);
        }
        brandList.add("iPhone和小米手机都很好用");
        
        sourceMap.put("kw_brand", brandList);
        
        long startTime = System.currentTimeMillis();
        String kwBrand = processKwBrand(sourceMap);
        String result = extractThreeCBrands(kwBrand);
        long endTime = System.currentTimeMillis();
        
        assertTrue(result.contains("iPhone"));
        assertTrue(result.contains("小米"));
        assertTrue(endTime - startTime < 1000, "处理时间应该小于1秒");
    }
}
