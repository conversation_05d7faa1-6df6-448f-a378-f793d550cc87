package data.oppodataapi.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 3C品牌提取功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ThreeCBrandExtractionTest {

    /**
     * 测试3C品牌提取逻辑
     */
    @Test
    public void testExtractThreeCBrands() {
        // 模拟 extractThreeCBrands 方法的逻辑
        
        // 测试用例1：包含单个品牌
        String result1 = extractThreeCBrands("我使用的是iPhone手机");
        assertEquals("iPhone", result1);
        
        // 测试用例2：包含多个品牌
        String result2 = extractThreeCBrands("我有iPhone和小米手机");
        assertEquals("iPhone,小米", result2);
        
        // 测试用例3：包含大小写混合的品牌
        String result3 = extractThreeCBrands("OPPO和huawei都很好用");
        assertEquals("OPPO,huawei", result3);
        
        // 测试用例4：包含重复品牌（应该去重）
        String result4 = extractThreeCBrands("小米手机和xiaomi都是同一个品牌");
        assertEquals("小米,xiaomi", result4);
        
        // 测试用例5：不包含3C品牌
        String result5 = extractThreeCBrands("我喜欢可口可乐和百事可乐");
        assertEquals("", result5);
        
        // 测试用例6：空字符串
        String result6 = extractThreeCBrands("");
        assertEquals("", result6);
        
        // 测试用例7：null值
        String result7 = extractThreeCBrands(null);
        assertEquals("", result7);
        
        // 测试用例8：包含所有支持的品牌
        String result8 = extractThreeCBrands("vivo小米xiaomiOPPO华为huawei三星Samsung红米redmi一加oneplus荣耀HONOR苹果iPhoneApple");
        assertTrue(result8.contains("vivo"));
        assertTrue(result8.contains("小米"));
        assertTrue(result8.contains("OPPO"));
        assertTrue(result8.contains("华为"));
        assertTrue(result8.contains("三星"));
        assertTrue(result8.contains("红米"));
        assertTrue(result8.contains("一加"));
        assertTrue(result8.contains("荣耀"));
        assertTrue(result8.contains("苹果"));
        assertTrue(result8.contains("iPhone"));
    }
    
    /**
     * 模拟 extractThreeCBrands 方法的实现
     * 这里复制了实际的实现逻辑用于测试
     */
    private String extractThreeCBrands(String kwBrand) {
        if (kwBrand == null || kwBrand.trim().isEmpty()) {
            return "";
        }
        
        // 定义3C品牌关键词（不区分大小写）
        String[] threeCBrandKeywords = {
            "vivo", "小米", "xiaomi", "OPPO", "华为", "huawei", 
            "三星", "Samsung", "红米", "redmi", "一加", "oneplus", 
            "荣耀", "HONOR", "苹果", "iPhone", "Apple"
        };
        
        // 使用LinkedHashSet保持顺序并去重
        java.util.Set<String> matchedBrands = new java.util.LinkedHashSet<>();
        
        // 将kwBrand转换为小写进行匹配
        String kwBrandLower = kwBrand.toLowerCase();
        
        // 遍历所有3C品牌关键词
        for (String keyword : threeCBrandKeywords) {
            String keywordLower = keyword.toLowerCase();
            
            // 检查是否包含该关键词
            if (kwBrandLower.contains(keywordLower)) {
                // 添加原始关键词（保持原有大小写）
                matchedBrands.add(keyword);
            }
        }
        
        // 将匹配的品牌用逗号连接
        return String.join(",", matchedBrands);
    }
    
    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        // 测试只有空格的字符串
        String result1 = extractThreeCBrands("   ");
        assertEquals("", result1);
        
        // 测试包含特殊字符的品牌
        String result2 = extractThreeCBrands("我的iPhone-12很好用");
        assertEquals("iPhone", result2);
        
        // 测试品牌名称在句子中间
        String result3 = extractThreeCBrands("今天我买了一台小米电视");
        assertEquals("小米", result3);
        
        // 测试品牌名称在句子开头
        String result4 = extractThreeCBrands("OPPO手机拍照很清晰");
        assertEquals("OPPO", result4);
        
        // 测试品牌名称在句子结尾
        String result5 = extractThreeCBrands("我最喜欢的品牌是华为");
        assertEquals("华为", result5);
    }
    
    /**
     * 测试性能（大字符串）
     */
    @Test
    public void testPerformance() {
        // 构建一个包含大量文本的字符串
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("这是一段很长的文本，");
        }
        sb.append("我使用iPhone和小米手机");
        
        long startTime = System.currentTimeMillis();
        String result = extractThreeCBrands(sb.toString());
        long endTime = System.currentTimeMillis();
        
        assertEquals("iPhone,小米", result);
        assertTrue(endTime - startTime < 100, "提取时间应该小于100ms");
    }
}
