<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="data.oppodataapi.mapper.OppoDataMapper">

    <!-- 批量插入OppoData数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO oppo_data (
                    user_nickname, user_id, douyin_id, star_map_id, user_avatar, mcn,
                    talent_description, star_map_link, douyin_link, followers_count,
                    short_video_price, long_video_price, extra_long_video_price, image_article_price,
                    expected_cpm_20_to_60_video, expected_cpe_20_to_60_video, expected_play_count_20_to_60_video,
                    viral_rate, talent_status_tag, talent_social_status_tag, talent_type,
                    content_theme, industry_tags, content_first_level_tag_for_commercial,
                    content_second_level_tag_for_commercial, content_first_level_tag_for_non_commercial,
                    content_second_level_tag_for_non_commercial, monthly_connected_user_count,
                    monthly_understood_user_count, monthly_interested_user_count, monthly_liked_user_count,
                    monthly_followed_user_count, monthly_connected_user_count_30_day_ratio,
                    monthly_understood_user_count_30_day_ratio, monthly_interested_user_count_30_day_ratio,
                    monthly_liked_user_count_30_day_ratio, monthly_followed_user_count_30_day_ratio,
                    fans_increment_rate_15_days, fans_increment_rate_30_days, fans_growth_count_last_30_days,
                    non_commercial_play_median_90_days, non_commercial_completion_rate_90_days,
                    non_commercial_interaction_rate_90_days, non_commercial_interaction_volume_90_days,
                    non_commercial_play_median_30_days, non_commercial_completion_rate_30_days,
                    non_commercial_interaction_rate_30_days, non_commercial_interaction_volume_30_days,
                    non_commercial_posted_works_count_90_days, non_commercial_average_duration_90_days,
                    non_commercial_average_forward_90_days, non_commercial_average_comment_90_days,
                    non_commercial_average_like_90_days, non_commercial_posted_works_count_30_days,
                    non_commercial_average_duration_30_days, non_commercial_average_forward_30_days,
                    non_commercial_average_comment_30_days, non_commercial_average_like_30_days,
                    commercial_play_median_90_days, commercial_completion_rate_90_days,
                    commercial_interaction_rate_90_days, commercial_interaction_volume_90_days,
                    commercial_play_median_30_days, commercial_completion_rate_30_days,
                    commercial_interaction_rate_30_days, commercial_interaction_volume_30_days,
                    commercial_posted_works_count_90_days, commercial_average_duration_90_days,
                    commercial_average_forward_90_days, commercial_average_comment_90_days,
                    commercial_average_like_90_days, commercial_posted_works_count_30_days,
                    commercial_average_duration_30_days, commercial_average_forward_30_days,
                    commercial_average_comment_30_days, commercial_average_like_30_days,
                    object_star_object_fans_distribution, object_star_object_viewers_distribution,
                    fans_device_distribution, fans_region_distribution, fans_city_distribution,
                    fans_gender_distribution, fans_age_distribution, fans_city_level, fans_eight_groups,
                    viewers_gender_distribution, viewers_age_distribution, viewers_region_distribution,
                    viewers_city_distribution, viewers_city_level_distribution, viewers_device_distribution,
                    viewers_eight_groups_distribution, commercial_cooperation_brands, three_c_commercial_cooperation_brands,
                    created_time, updated_time
                ) VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.userNickname}, #{item.userId}, #{item.douyinId}, #{item.starMapId}, #{item.userAvatar}, #{item.mcn},
                    #{item.talentDescription}, #{item.starMapLink}, #{item.douyinLink}, #{item.followersCount},
                    #{item.shortVideoPrice}, #{item.longVideoPrice}, #{item.extraLongVideoPrice}, #{item.imageArticlePrice},
                    #{item.expectedCpm20To60Video}, #{item.expectedCpe20To60Video}, #{item.expectedPlayCount20To60Video},
                    #{item.viralRate}, #{item.talentStatusTag}, #{item.talentSocialStatusTag}, #{item.talentType},
                    #{item.contentTheme}, #{item.industryTags}, #{item.contentFirstLevelTagForCommercial},
                    #{item.contentSecondLevelTagForCommercial}, #{item.contentFirstLevelTagForNonCommercial},
                    #{item.contentSecondLevelTagForNonCommercial}, #{item.monthlyConnectedUserCount},
                    #{item.monthlyUnderstoodUserCount}, #{item.monthlyInterestedUserCount}, #{item.monthlyLikedUserCount},
                    #{item.monthlyFollowedUserCount}, #{item.monthlyConnectedUserCount30DayRatio},
                    #{item.monthlyUnderstoodUserCount30DayRatio}, #{item.monthlyInterestedUserCount30DayRatio},
                    #{item.monthlyLikedUserCount30DayRatio}, #{item.monthlyFollowedUserCount30DayRatio},
                    #{item.fansIncrementRate15Days}, #{item.fansIncrementRate30Days}, #{item.fansGrowthCountLast30Days},
                    #{item.nonCommercialPlayMedian90Days}, #{item.nonCommercialCompletionRate90Days},
                    #{item.nonCommercialInteractionRate90Days}, #{item.nonCommercialInteractionVolume90Days},
                    #{item.nonCommercialPlayMedian30Days}, #{item.nonCommercialCompletionRate30Days},
                    #{item.nonCommercialInteractionRate30Days}, #{item.nonCommercialInteractionVolume30Days},
                    #{item.nonCommercialPostedWorksCount90Days}, #{item.nonCommercialAverageDuration90Days},
                    #{item.nonCommercialAverageForward90Days}, #{item.nonCommercialAverageComment90Days},
                    #{item.nonCommercialAverageLike90Days}, #{item.nonCommercialPostedWorksCount30Days},
                    #{item.nonCommercialAverageDuration30Days}, #{item.nonCommercialAverageForward30Days},
                    #{item.nonCommercialAverageComment30Days}, #{item.nonCommercialAverageLike30Days},
                    #{item.commercialPlayMedian90Days}, #{item.commercialCompletionRate90Days},
                    #{item.commercialInteractionRate90Days}, #{item.commercialInteractionVolume90Days},
                    #{item.commercialPlayMedian30Days}, #{item.commercialCompletionRate30Days},
                    #{item.commercialInteractionRate30Days}, #{item.commercialInteractionVolume30Days},
                    #{item.commercialPostedWorksCount90Days}, #{item.commercialAverageDuration90Days},
                    #{item.commercialAverageForward90Days}, #{item.commercialAverageComment90Days},
                    #{item.commercialAverageLike90Days}, #{item.commercialPostedWorksCount30Days},
                    #{item.commercialAverageDuration30Days}, #{item.commercialAverageForward30Days},
                    #{item.commercialAverageComment30Days}, #{item.commercialAverageLike30Days},
                    #{item.objectStarObjectFansDistribution}, #{item.objectStarObjectViewersDistribution},
                    #{item.fansDeviceDistribution}, #{item.fansRegionDistribution}, #{item.fansCityDistribution},
                    #{item.fansGenderDistribution}, #{item.fansAgeDistribution}, #{item.fansCityLevel}, #{item.fansEightGroups},
                    #{item.viewersGenderDistribution}, #{item.viewersAgeDistribution}, #{item.viewersRegionDistribution},
                    #{item.viewersCityDistribution}, #{item.viewersCityLevelDistribution}, #{item.viewersDeviceDistribution},
                    #{item.viewersEightGroupsDistribution}, #{item.commercialCooperationBrands}, #{item.threeCCommercialCooperationBrands},
                    #{item.createTime}, #{item.updatedTime}
                )
                </foreach>
                ON DUPLICATE KEY UPDATE
                user_nickname = VALUES(user_nickname),
                douyin_id = VALUES(douyin_id),
                user_avatar = VALUES(user_avatar),
                mcn = VALUES(mcn),
                talent_description = VALUES(talent_description),
                star_map_link = VALUES(star_map_link),
                douyin_link = VALUES(douyin_link),
                followers_count = VALUES(followers_count),
                short_video_price = VALUES(short_video_price),
                long_video_price = VALUES(long_video_price),
                extra_long_video_price = VALUES(extra_long_video_price),
                image_article_price = VALUES(image_article_price),
                expected_cpm_20_to_60_video = VALUES(expected_cpm_20_to_60_video),
                expected_cpe_20_to_60_video = VALUES(expected_cpe_20_to_60_video),
                expected_play_count_20_to_60_video = VALUES(expected_play_count_20_to_60_video),
                viral_rate = VALUES(viral_rate),
                talent_status_tag = VALUES(talent_status_tag),
                talent_social_status_tag = VALUES(talent_social_status_tag),
                talent_type = VALUES(talent_type),
                content_theme = VALUES(content_theme),
                industry_tags = VALUES(industry_tags),
                content_first_level_tag_for_commercial = VALUES(content_first_level_tag_for_commercial),
                content_second_level_tag_for_commercial = VALUES(content_second_level_tag_for_commercial),
                content_first_level_tag_for_non_commercial = VALUES(content_first_level_tag_for_non_commercial),
                content_second_level_tag_for_non_commercial = VALUES(content_second_level_tag_for_non_commercial),
                monthly_connected_user_count = VALUES(monthly_connected_user_count),
                monthly_understood_user_count = VALUES(monthly_understood_user_count),
                monthly_interested_user_count = VALUES(monthly_interested_user_count),
                monthly_liked_user_count = VALUES(monthly_liked_user_count),
                monthly_followed_user_count = VALUES(monthly_followed_user_count),
                monthly_connected_user_count_30_day_ratio = VALUES(monthly_connected_user_count_30_day_ratio),
                monthly_understood_user_count_30_day_ratio = VALUES(monthly_understood_user_count_30_day_ratio),
                monthly_interested_user_count_30_day_ratio = VALUES(monthly_interested_user_count_30_day_ratio),
                monthly_liked_user_count_30_day_ratio = VALUES(monthly_liked_user_count_30_day_ratio),
                monthly_followed_user_count_30_day_ratio = VALUES(monthly_followed_user_count_30_day_ratio),
                fans_increment_rate_15_days = VALUES(fans_increment_rate_15_days),
                fans_increment_rate_30_days = VALUES(fans_increment_rate_30_days),
                fans_growth_count_last_30_days = VALUES(fans_growth_count_last_30_days),
                non_commercial_play_median_90_days = VALUES(non_commercial_play_median_90_days),
                non_commercial_completion_rate_90_days = VALUES(non_commercial_completion_rate_90_days),
                non_commercial_interaction_rate_90_days = VALUES(non_commercial_interaction_rate_90_days),
                non_commercial_interaction_volume_90_days = VALUES(non_commercial_interaction_volume_90_days),
                non_commercial_play_median_30_days = VALUES(non_commercial_play_median_30_days),
                non_commercial_completion_rate_30_days = VALUES(non_commercial_completion_rate_30_days),
                non_commercial_interaction_rate_30_days = VALUES(non_commercial_interaction_rate_30_days),
                non_commercial_interaction_volume_30_days = VALUES(non_commercial_interaction_volume_30_days),
                non_commercial_posted_works_count_90_days = VALUES(non_commercial_posted_works_count_90_days),
                non_commercial_average_duration_90_days = VALUES(non_commercial_average_duration_90_days),
                non_commercial_average_forward_90_days = VALUES(non_commercial_average_forward_90_days),
                non_commercial_average_comment_90_days = VALUES(non_commercial_average_comment_90_days),
                non_commercial_average_like_90_days = VALUES(non_commercial_average_like_90_days),
                non_commercial_posted_works_count_30_days = VALUES(non_commercial_posted_works_count_30_days),
                non_commercial_average_duration_30_days = VALUES(non_commercial_average_duration_30_days),
                non_commercial_average_forward_30_days = VALUES(non_commercial_average_forward_30_days),
                non_commercial_average_comment_30_days = VALUES(non_commercial_average_comment_30_days),
                non_commercial_average_like_30_days = VALUES(non_commercial_average_like_30_days),
                commercial_play_median_90_days = VALUES(commercial_play_median_90_days),
                commercial_completion_rate_90_days = VALUES(commercial_completion_rate_90_days),
                commercial_interaction_rate_90_days = VALUES(commercial_interaction_rate_90_days),
                commercial_interaction_volume_90_days = VALUES(commercial_interaction_volume_90_days),
                commercial_play_median_30_days = VALUES(commercial_play_median_30_days),
                commercial_completion_rate_30_days = VALUES(commercial_completion_rate_30_days),
                commercial_interaction_rate_30_days = VALUES(commercial_interaction_rate_30_days),
                commercial_interaction_volume_30_days = VALUES(commercial_interaction_volume_30_days),
                commercial_posted_works_count_90_days = VALUES(commercial_posted_works_count_90_days),
                commercial_average_duration_90_days = VALUES(commercial_average_duration_90_days),
                commercial_average_forward_90_days = VALUES(commercial_average_forward_90_days),
                commercial_average_comment_90_days = VALUES(commercial_average_comment_90_days),
                commercial_average_like_90_days = VALUES(commercial_average_like_90_days),
                commercial_posted_works_count_30_days = VALUES(commercial_posted_works_count_30_days),
                commercial_average_duration_30_days = VALUES(commercial_average_duration_30_days),
                commercial_average_forward_30_days = VALUES(commercial_average_forward_30_days),
                commercial_average_comment_30_days = VALUES(commercial_average_comment_30_days),
                commercial_average_like_30_days = VALUES(commercial_average_like_30_days),
                object_star_object_fans_distribution = VALUES(object_star_object_fans_distribution),
                object_star_object_viewers_distribution = VALUES(object_star_object_viewers_distribution),
                fans_device_distribution = VALUES(fans_device_distribution),
                fans_region_distribution = VALUES(fans_region_distribution),
                fans_city_distribution = VALUES(fans_city_distribution),
                fans_gender_distribution = VALUES(fans_gender_distribution),
                fans_age_distribution = VALUES(fans_age_distribution),
                fans_city_level = VALUES(fans_city_level),
                fans_eight_groups = VALUES(fans_eight_groups),
                viewers_gender_distribution = VALUES(viewers_gender_distribution),
                viewers_age_distribution = VALUES(viewers_age_distribution),
                viewers_region_distribution = VALUES(viewers_region_distribution),
                viewers_city_distribution = VALUES(viewers_city_distribution),
                viewers_city_level_distribution = VALUES(viewers_city_level_distribution),
                viewers_device_distribution = VALUES(viewers_device_distribution),
                viewers_eight_groups_distribution = VALUES(viewers_eight_groups_distribution),
                commercial_cooperation_brands = VALUES(commercial_cooperation_brands),
                three_c_commercial_cooperation_brands = VALUES(three_c_commercial_cooperation_brands),
                updated_time = NOW()
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 动态表名查询 - 根据用户ID查询数据 -->
    <select id="selectByUserIdFromTable" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName} WHERE user_id = #{userId}
    </select>

    <!-- 动态表名查询 - 根据用户ID列表批量查询数据 -->
    <select id="selectByUserIdsFromTable" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        ORDER BY created_time DESC
    </select>

    <!-- 动态表名分页查询 - 根据用户ID列表分页查询数据 -->
    <select id="selectByUserIdsFromTableWithPage" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        ORDER BY created_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 动态表名统计 - 根据用户ID列表统计数据数量 -->
    <select id="countByUserIdsFromTable" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <!-- 动态表名查询 - 根据星图ID查询数据 -->
    <select id="selectByStarMapIdFromTable" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName} WHERE star_map_id = #{starMapId} LIMIT 1
    </select>

    <!-- 动态表名查询 - 查询所有数据 -->
    <select id="selectAllFromTable" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
    </select>

    <!-- 动态表名查询 - 分页查询 -->
    <select id="selectPageFromTable" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName} LIMIT #{offset}, #{limit}
    </select>

    <!-- 动态表名查询 - 统计记录数 -->
    <select id="countFromTable" resultType="long">
        SELECT COUNT(*) FROM ${tableName}
    </select>

    <!-- 检查表是否存在 -->
    <select id="checkTableExists" resultType="int">
        SELECT COUNT(*) FROM information_schema.tables
        WHERE table_schema = DATABASE() AND table_name = #{tableName}
    </select>

    <!-- 创建周表 -->
    <update id="createWeeklyTable">
        CREATE TABLE IF NOT EXISTS ${tableName} LIKE ${templateTableName}
    </update>

    <!-- 动态表名批量插入 -->
    <insert id="batchInsertToTable" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO ${tableName} (
                    user_nickname, user_id, douyin_id, star_map_id, user_avatar, mcn,
                    talent_description, star_map_link, douyin_link, followers_count,
                    short_video_price, long_video_price, extra_long_video_price, image_article_price,
                    expected_cpm_20_to_60_video, expected_cpe_20_to_60_video, expected_play_count_20_to_60_video,
                    viral_rate, talent_status_tag, talent_social_status_tag, talent_type,
                    content_theme, industry_tags, content_first_level_tag_for_commercial,
                    content_second_level_tag_for_commercial, content_first_level_tag_for_non_commercial,
                    content_second_level_tag_for_non_commercial, monthly_connected_user_count,
                    monthly_understood_user_count, monthly_interested_user_count, monthly_liked_user_count,
                    monthly_followed_user_count, monthly_connected_user_count_30_day_ratio,
                    monthly_understood_user_count_30_day_ratio, monthly_interested_user_count_30_day_ratio,
                    monthly_liked_user_count_30_day_ratio, monthly_followed_user_count_30_day_ratio,
                    fans_increment_rate_15_days, fans_increment_rate_30_days, fans_growth_count_last_30_days,
                    non_commercial_play_median_90_days, non_commercial_completion_rate_90_days,
                    non_commercial_interaction_rate_90_days, non_commercial_interaction_volume_90_days,
                    non_commercial_play_median_30_days, non_commercial_completion_rate_30_days,
                    non_commercial_interaction_rate_30_days, non_commercial_interaction_volume_30_days,
                    non_commercial_posted_works_count_90_days, non_commercial_average_duration_90_days,
                    non_commercial_average_forward_90_days, non_commercial_average_comment_90_days,
                    non_commercial_average_like_90_days, non_commercial_posted_works_count_30_days,
                    non_commercial_average_duration_30_days, non_commercial_average_forward_30_days,
                    non_commercial_average_comment_30_days, non_commercial_average_like_30_days,
                    commercial_play_median_90_days, commercial_completion_rate_90_days,
                    commercial_interaction_rate_90_days, commercial_interaction_volume_90_days,
                    commercial_play_median_30_days, commercial_completion_rate_30_days,
                    commercial_interaction_rate_30_days, commercial_interaction_volume_30_days,
                    commercial_posted_works_count_90_days, commercial_average_duration_90_days,
                    commercial_average_forward_90_days, commercial_average_comment_90_days,
                    commercial_average_like_90_days, commercial_posted_works_count_30_days,
                    commercial_average_duration_30_days, commercial_average_forward_30_days,
                    commercial_average_comment_30_days, commercial_average_like_30_days,
                    fans_device_distribution, fans_region_distribution, fans_city_distribution,
                    fans_gender_distribution, fans_age_distribution, fans_city_level, fans_eight_groups,
                    viewers_gender_distribution, viewers_age_distribution, viewers_region_distribution,
                    viewers_city_distribution, viewers_city_level_distribution, viewers_device_distribution,
                    viewers_eight_groups_distribution, commercial_cooperation_brands, three_c_commercial_cooperation_brands,
                    object_star_object_fans_distribution, object_star_object_viewers_distribution,
                    created_time, updated_time
                ) VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.userNickname}, #{item.userId}, #{item.douyinId}, #{item.starMapId}, #{item.userAvatar}, #{item.mcn},
                    #{item.talentDescription}, #{item.starMapLink}, #{item.douyinLink}, #{item.followersCount},
                    #{item.shortVideoPrice}, #{item.longVideoPrice}, #{item.extraLongVideoPrice}, #{item.imageArticlePrice},
                    #{item.expectedCpm20To60Video}, #{item.expectedCpe20To60Video}, #{item.expectedPlayCount20To60Video},
                    #{item.viralRate}, #{item.talentStatusTag}, #{item.talentSocialStatusTag}, #{item.talentType},
                    #{item.contentTheme}, #{item.industryTags}, #{item.contentFirstLevelTagForCommercial},
                    #{item.contentSecondLevelTagForCommercial}, #{item.contentFirstLevelTagForNonCommercial},
                    #{item.contentSecondLevelTagForNonCommercial}, #{item.monthlyConnectedUserCount},
                    #{item.monthlyUnderstoodUserCount}, #{item.monthlyInterestedUserCount}, #{item.monthlyLikedUserCount},
                    #{item.monthlyFollowedUserCount}, #{item.monthlyConnectedUserCount30DayRatio},
                    #{item.monthlyUnderstoodUserCount30DayRatio}, #{item.monthlyInterestedUserCount30DayRatio},
                    #{item.monthlyLikedUserCount30DayRatio}, #{item.monthlyFollowedUserCount30DayRatio},
                    #{item.fansIncrementRate15Days}, #{item.fansIncrementRate30Days}, #{item.fansGrowthCountLast30Days},
                    #{item.nonCommercialPlayMedian90Days}, #{item.nonCommercialCompletionRate90Days},
                    #{item.nonCommercialInteractionRate90Days}, #{item.nonCommercialInteractionVolume90Days},
                    #{item.nonCommercialPlayMedian30Days}, #{item.nonCommercialCompletionRate30Days},
                    #{item.nonCommercialInteractionRate30Days}, #{item.nonCommercialInteractionVolume30Days},
                    #{item.nonCommercialPostedWorksCount90Days}, #{item.nonCommercialAverageDuration90Days},
                    #{item.nonCommercialAverageForward90Days}, #{item.nonCommercialAverageComment90Days},
                    #{item.nonCommercialAverageLike90Days}, #{item.nonCommercialPostedWorksCount30Days},
                    #{item.nonCommercialAverageDuration30Days}, #{item.nonCommercialAverageForward30Days},
                    #{item.nonCommercialAverageComment30Days}, #{item.nonCommercialAverageLike30Days},
                    #{item.commercialPlayMedian90Days}, #{item.commercialCompletionRate90Days},
                    #{item.commercialInteractionRate90Days}, #{item.commercialInteractionVolume90Days},
                    #{item.commercialPlayMedian30Days}, #{item.commercialCompletionRate30Days},
                    #{item.commercialInteractionRate30Days}, #{item.commercialInteractionVolume30Days},
                    #{item.commercialPostedWorksCount90Days}, #{item.commercialAverageDuration90Days},
                    #{item.commercialAverageForward90Days}, #{item.commercialAverageComment90Days},
                    #{item.commercialAverageLike90Days}, #{item.commercialPostedWorksCount30Days},
                    #{item.commercialAverageDuration30Days}, #{item.commercialAverageForward30Days},
                    #{item.commercialAverageComment30Days}, #{item.commercialAverageLike30Days},
                    #{item.fansDeviceDistribution}, #{item.fansRegionDistribution}, #{item.fansCityDistribution},
                    #{item.fansGenderDistribution}, #{item.fansAgeDistribution}, #{item.fansCityLevel}, #{item.fansEightGroups},
                    #{item.viewersGenderDistribution}, #{item.viewersAgeDistribution}, #{item.viewersRegionDistribution},
                    #{item.viewersCityDistribution}, #{item.viewersCityLevelDistribution}, #{item.viewersDeviceDistribution},
                    #{item.viewersEightGroupsDistribution}, #{item.commercialCooperationBrands}, #{item.threeCCommercialCooperationBrands},
                    #{item.objectStarObjectFansDistribution}, #{item.objectStarObjectViewersDistribution},
                    #{item.createTime}, #{item.updatedTime}
                )
                </foreach>
                ON DUPLICATE KEY UPDATE
                user_nickname = VALUES(user_nickname),
                douyin_id = VALUES(douyin_id),
                user_avatar = VALUES(user_avatar),
                mcn = VALUES(mcn),
                talent_description = VALUES(talent_description),
                star_map_link = VALUES(star_map_link),
                douyin_link = VALUES(douyin_link),
                followers_count = VALUES(followers_count),
                short_video_price = VALUES(short_video_price),
                long_video_price = VALUES(long_video_price),
                extra_long_video_price = VALUES(extra_long_video_price),
                image_article_price = VALUES(image_article_price),
                expected_cpm_20_to_60_video = VALUES(expected_cpm_20_to_60_video),
                expected_cpe_20_to_60_video = VALUES(expected_cpe_20_to_60_video),
                expected_play_count_20_to_60_video = VALUES(expected_play_count_20_to_60_video),
                viral_rate = VALUES(viral_rate),
                talent_status_tag = VALUES(talent_status_tag),
                talent_social_status_tag = VALUES(talent_social_status_tag),
                talent_type = VALUES(talent_type),
                content_theme = VALUES(content_theme),
                industry_tags = VALUES(industry_tags),
                content_first_level_tag_for_commercial = VALUES(content_first_level_tag_for_commercial),
                content_second_level_tag_for_commercial = VALUES(content_second_level_tag_for_commercial),
                content_first_level_tag_for_non_commercial = VALUES(content_first_level_tag_for_non_commercial),
                content_second_level_tag_for_non_commercial = VALUES(content_second_level_tag_for_non_commercial),
                monthly_connected_user_count = VALUES(monthly_connected_user_count),
                monthly_understood_user_count = VALUES(monthly_understood_user_count),
                monthly_interested_user_count = VALUES(monthly_interested_user_count),
                monthly_liked_user_count = VALUES(monthly_liked_user_count),
                monthly_followed_user_count = VALUES(monthly_followed_user_count),
                monthly_connected_user_count_30_day_ratio = VALUES(monthly_connected_user_count_30_day_ratio),
                monthly_understood_user_count_30_day_ratio = VALUES(monthly_understood_user_count_30_day_ratio),
                monthly_interested_user_count_30_day_ratio = VALUES(monthly_interested_user_count_30_day_ratio),
                monthly_liked_user_count_30_day_ratio = VALUES(monthly_liked_user_count_30_day_ratio),
                monthly_followed_user_count_30_day_ratio = VALUES(monthly_followed_user_count_30_day_ratio),
                fans_increment_rate_15_days = VALUES(fans_increment_rate_15_days),
                fans_increment_rate_30_days = VALUES(fans_increment_rate_30_days),
                fans_growth_count_last_30_days = VALUES(fans_growth_count_last_30_days),
                non_commercial_play_median_90_days = VALUES(non_commercial_play_median_90_days),
                non_commercial_completion_rate_90_days = VALUES(non_commercial_completion_rate_90_days),
                non_commercial_interaction_rate_90_days = VALUES(non_commercial_interaction_rate_90_days),
                non_commercial_interaction_volume_90_days = VALUES(non_commercial_interaction_volume_90_days),
                non_commercial_play_median_30_days = VALUES(non_commercial_play_median_30_days),
                non_commercial_completion_rate_30_days = VALUES(non_commercial_completion_rate_30_days),
                non_commercial_interaction_rate_30_days = VALUES(non_commercial_interaction_rate_30_days),
                non_commercial_interaction_volume_30_days = VALUES(non_commercial_interaction_volume_30_days),
                non_commercial_posted_works_count_90_days = VALUES(non_commercial_posted_works_count_90_days),
                non_commercial_average_duration_90_days = VALUES(non_commercial_average_duration_90_days),
                non_commercial_average_forward_90_days = VALUES(non_commercial_average_forward_90_days),
                non_commercial_average_comment_90_days = VALUES(non_commercial_average_comment_90_days),
                non_commercial_average_like_90_days = VALUES(non_commercial_average_like_90_days),
                non_commercial_posted_works_count_30_days = VALUES(non_commercial_posted_works_count_30_days),
                non_commercial_average_duration_30_days = VALUES(non_commercial_average_duration_30_days),
                non_commercial_average_forward_30_days = VALUES(non_commercial_average_forward_30_days),
                non_commercial_average_comment_30_days = VALUES(non_commercial_average_comment_30_days),
                non_commercial_average_like_30_days = VALUES(non_commercial_average_like_30_days),
                commercial_play_median_90_days = VALUES(commercial_play_median_90_days),
                commercial_completion_rate_90_days = VALUES(commercial_completion_rate_90_days),
                commercial_interaction_rate_90_days = VALUES(commercial_interaction_rate_90_days),
                commercial_interaction_volume_90_days = VALUES(commercial_interaction_volume_90_days),
                commercial_play_median_30_days = VALUES(commercial_play_median_30_days),
                commercial_completion_rate_30_days = VALUES(commercial_completion_rate_30_days),
                commercial_interaction_rate_30_days = VALUES(commercial_interaction_rate_30_days),
                commercial_interaction_volume_30_days = VALUES(commercial_interaction_volume_30_days),
                commercial_posted_works_count_90_days = VALUES(commercial_posted_works_count_90_days),
                commercial_average_duration_90_days = VALUES(commercial_average_duration_90_days),
                commercial_average_forward_90_days = VALUES(commercial_average_forward_90_days),
                commercial_average_comment_90_days = VALUES(commercial_average_comment_90_days),
                commercial_average_like_90_days = VALUES(commercial_average_like_90_days),
                commercial_posted_works_count_30_days = VALUES(commercial_posted_works_count_30_days),
                commercial_average_duration_30_days = VALUES(commercial_average_duration_30_days),
                commercial_average_forward_30_days = VALUES(commercial_average_forward_30_days),
                commercial_average_comment_30_days = VALUES(commercial_average_comment_30_days),
                commercial_average_like_30_days = VALUES(commercial_average_like_30_days),
                object_star_object_fans_distribution = VALUES(object_star_object_fans_distribution),
                object_star_object_viewers_distribution = VALUES(object_star_object_viewers_distribution),
                fans_device_distribution = VALUES(fans_device_distribution),
                fans_region_distribution = VALUES(fans_region_distribution),
                fans_city_distribution = VALUES(fans_city_distribution),
                fans_gender_distribution = VALUES(fans_gender_distribution),
                fans_age_distribution = VALUES(fans_age_distribution),
                fans_city_level = VALUES(fans_city_level),
                fans_eight_groups = VALUES(fans_eight_groups),
                viewers_gender_distribution = VALUES(viewers_gender_distribution),
                viewers_age_distribution = VALUES(viewers_age_distribution),
                viewers_region_distribution = VALUES(viewers_region_distribution),
                viewers_city_distribution = VALUES(viewers_city_distribution),
                viewers_city_level_distribution = VALUES(viewers_city_level_distribution),
                viewers_device_distribution = VALUES(viewers_device_distribution),
                viewers_eight_groups_distribution = VALUES(viewers_eight_groups_distribution),
                commercial_cooperation_brands = VALUES(commercial_cooperation_brands),
                three_c_commercial_cooperation_brands = VALUES(three_c_commercial_cooperation_brands),
                updated_time = NOW()
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 兼容性接口 - 使用当前周表 -->
    <!-- 根据用户ID查询数据 -->
    <select id="selectByUserId" parameterType="string" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM oppo_data WHERE user_id = #{userId}
    </select>

    <!-- 根据星图ID查询数据 -->
    <select id="selectByStarMapId" parameterType="string" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM oppo_data WHERE star_map_id = #{starMapId} LIMIT 1
    </select>

    <!-- 优化的分页查询OPPO数据（支持多条件查询） -->
    <select id="selectPageOptimized" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
        <where>
            <if test="request.batchId != null and request.batchId != ''">
                AND batch_id = #{request.batchId}
            </if>

            <!-- 处理用户查询条件列表（新的方式：每个条件查询一条数据） -->
            <if test="request.userCondition != null and request.userCondition.size() > 0">
                AND (
                <trim suffixOverrides="OR">
                    <foreach collection="request.userCondition" item="condition" separator="OR">
                        <if test="condition.hasValidCondition">
                            (
                            <trim suffixOverrides="OR">
                                <!-- 处理userId -->
                                <if test="condition.userId != null and condition.userId != ''">
                                    user_id = #{condition.userId}
                                    OR
                                </if>

                                <!-- 处理douyinId -->
                                <if test="condition.douyinId != null and condition.douyinId != ''">
                                    douyin_id = #{condition.douyinId}
                                    OR
                                </if>

                                <!-- 处理starMapId -->
                                <if test="condition.starMapId != null and condition.starMapId != ''">
                                    star_map_id = #{condition.starMapId}
                                    OR
                                </if>

                                <!-- 处理douyinLink -->
                                <if test="condition.douyinLink != null and condition.douyinLink != ''">
                                    douyin_link = #{condition.douyinLink}
                                    OR
                                </if>
                            </trim>
                            )
                        </if>
                    </foreach>
                </trim>
                )
            </if>

            <!-- 处理原有的userIds字符串参数（逗号分隔）- 向后兼容 -->
            <if test="request.userCondition == null and request.userIds != null and request.userIds != ''">
                AND (user_id IN
                <foreach collection="request.userIds.split(',')" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                )
            </if>

            <!-- 处理新的userIdList数组参数 - 向后兼容 -->
            <if test="request.userCondition == null and request.userIdList != null and request.userIdList.size() > 0">
                AND (user_id IN
                <foreach collection="request.userIdList" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                )
            </if>

            <!-- 处理抖音ID列表 - 向后兼容 -->
            <if test="request.userCondition == null and request.douyinIds != null and request.douyinIds.size() > 0">
                AND (douyin_id IN
                <foreach collection="request.douyinIds" item="douyinId" open="(" separator="," close=")">
                    #{douyinId}
                </foreach>
                )
            </if>

            <!-- 处理星图ID列表 - 向后兼容 -->
            <if test="request.userCondition == null and request.starMapIds != null and request.starMapIds.size() > 0">
                AND (star_map_id IN
                <foreach collection="request.starMapIds" item="starMapId" open="(" separator="," close=")">
                    #{starMapId}
                </foreach>
                )
            </if>

            <!-- 处理时间范围查询 -->
            <if test="request.startUploadTime != null">
                AND updated_time >= #{request.startUploadTime}
            </if>
            <if test="request.endUploadTime != null">
                AND updated_time &lt;= #{request.endUploadTime}
            </if>

            <!-- 处理关键词搜索 -->
            <if test="request.keyword != null and request.keyword != ''">
                AND (user_nickname LIKE CONCAT('%', #{request.keyword}, '%')
                     OR douyin_id LIKE CONCAT('%', #{request.keyword}, '%')
                     OR star_map_id LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>

<!--            &lt;!&ndash; 处理平台过滤 &ndash;&gt;-->
<!--            <if test="request.platform != null and request.platform != ''">-->
<!--                AND platform = #{request.platform}-->
<!--            </if>-->
        </where>

        <!-- 处理排序 -->
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy}
                <choose>
                    <when test="request.orderDirection != null and request.orderDirection.toUpperCase() == 'ASC'">
                        ASC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY updated_time DESC
            </otherwise>
        </choose>

        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 优化的统计OPPO数据数量（支持多条件查询） -->
    <select id="countOptimized" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        <where>
            <if test="request.batchId != null and request.batchId != ''">
                AND batch_id = #{request.batchId}
            </if>

            <!-- 处理用户查询条件列表（新的方式：每个条件查询一条数据） -->
            <if test="request.userCondition != null and request.userCondition.size() > 0">
                AND (
                <trim suffixOverrides="OR">
                    <foreach collection="request.userCondition" item="condition" separator="OR">
                        <if test="condition.hasValidCondition">
                            (
                            <trim suffixOverrides="OR">
                                <!-- 处理userId -->
                                <if test="condition.userId != null and condition.userId != ''">
                                    user_id = #{condition.userId}
                                    OR
                                </if>

                                <!-- 处理douyinId -->
                                <if test="condition.douyinId != null and condition.douyinId != ''">
                                    douyin_id = #{condition.douyinId}
                                    OR
                                </if>

                                <!-- 处理starMapId -->
                                <if test="condition.starMapId != null and condition.starMapId != ''">
                                    star_map_id = #{condition.starMapId}
                                    OR
                                </if>

                                <!-- 处理douyinLink -->
                                <if test="condition.douyinLink != null and condition.douyinLink != ''">
                                    douyin_link = #{condition.douyinLink}
                                    OR
                                </if>
                            </trim>
                            )
                        </if>
                    </foreach>
                </trim>
                )
            </if>

            <!-- 处理原有的userIds字符串参数（逗号分隔）- 向后兼容 -->
            <if test="request.userCondition == null and request.userIds != null and request.userIds != ''">
                AND (user_id IN
                <foreach collection="request.userIds.split(',')" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                )
            </if>

            <!-- 处理新的userIdList数组参数 - 向后兼容 -->
            <if test="request.userCondition == null and request.userIdList != null and request.userIdList.size() > 0">
                AND (user_id IN
                <foreach collection="request.userIdList" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                )
            </if>

            <!-- 处理抖音ID列表 - 向后兼容 -->
            <if test="request.userCondition == null and request.douyinIds != null and request.douyinIds.size() > 0">
                AND (douyin_id IN
                <foreach collection="request.douyinIds" item="douyinId" open="(" separator="," close=")">
                    #{douyinId}
                </foreach>
                )
            </if>

            <!-- 处理星图ID列表 - 向后兼容 -->
            <if test="request.userCondition == null and request.starMapIds != null and request.starMapIds.size() > 0">
                AND (star_map_id IN
                <foreach collection="request.starMapIds" item="starMapId" open="(" separator="," close=")">
                    #{starMapId}
                </foreach>
                )
            </if>

            <!-- 处理时间范围查询 -->
            <if test="request.startUploadTime != null">
                AND updated_time >= #{request.startUploadTime}
            </if>
            <if test="request.endUploadTime != null">
                AND updated_time &lt;= #{request.endUploadTime}
            </if>

            <!-- 处理关键词搜索 -->
            <if test="request.keyword != null and request.keyword != ''">
                AND (user_nickname LIKE CONCAT('%', #{request.keyword}, '%')
                     OR douyin_id LIKE CONCAT('%', #{request.keyword}, '%')
                     OR star_map_id LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>

            <!-- 处理平台过滤 -->
            <if test="request.platform != null and request.platform != ''">
                AND platform = #{request.platform}
            </if>
        </where>
    </select>

    <!-- 获取指定表中的记录总数 -->
    <select id="getTableRecordCount" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
    </select>

    <!-- 查找有画像数据的用户 -->
    <select id="selectUsersWithPortraitData" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
        WHERE (object_star_object_fans_distribution IS NOT NULL
               OR object_star_object_viewers_distribution IS NOT NULL
               OR fans_gender_distribution IS NOT NULL
               OR viewers_gender_distribution IS NOT NULL)
        ORDER BY updated_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据用户ID查询用户数据 -->
    <select id="selectByUserId" resultType="data.oppodataapi.entity.OppoData">
        SELECT * FROM ${tableName}
        WHERE user_id = #{userId}
        LIMIT 1
    </select>

</mapper>
