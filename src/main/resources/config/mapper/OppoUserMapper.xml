<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="data.oppodataapi.mapper.OppoUserMapper">
    
    <!-- 动态表名查询 - 根据抖音ID查询用户 -->
    <select id="selectByDouyinIdFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName} WHERE douyin_id = #{douyinId} AND deleted = 0
    </select>
    
    <!-- 动态表名查询 - 根据星图ID查询用户 -->
    <select id="selectByStarMapIdFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName} WHERE star_map_id = #{starMapId} AND deleted = 0
    </select>
    
    <!-- 动态表名查询 - 根据抖音ID列表批量查询用户 -->
    <select id="selectByDouyinIdsFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName}
        WHERE douyin_id IN
        <foreach collection="douyinIds" item="douyinId" open="(" separator="," close=")">
            #{douyinId}
        </foreach>
        AND deleted = 0
    </select>

    <!-- 动态表名查询 - 根据批次ID查询用户 -->
    <select id="selectByBatchIdFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName} WHERE batch_id = #{batchId} AND deleted = 0
    </select>
    
    <!-- 动态表名查询 - 查询所有用户 -->
    <select id="selectAllFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName} WHERE deleted = 0
    </select>
    
    <!-- 动态表名查询 - 分页查询 -->
    <select id="selectPageFromTable" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName} WHERE deleted = 0 LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 动态表名查询 - 统计记录数 -->
    <select id="countFromTable" resultType="long">
        SELECT COUNT(*) FROM ${tableName} WHERE deleted = 0
    </select>
    
    <!-- 检查表是否存在 -->
    <select id="checkTableExists" resultType="int">
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = #{tableName}
    </select>
    
    <!-- 创建月表 -->
    <update id="createMonthlyTable">
        CREATE TABLE IF NOT EXISTS ${tableName} LIKE ${templateTableName}
    </update>

    <!-- 创建周表 -->
    <update id="createWeeklyTable">
        CREATE TABLE IF NOT EXISTS ${tableName} LIKE ${templateTableName}
    </update>
    
    <!-- 插入单个用户到指定表 -->
    <insert id="insertToTable" parameterType="data.oppodataapi.entity.OppoUser" useGeneratedKeys="true" keyProperty="user.id">
        INSERT INTO ${tableName} (douyin_id, star_map_id, batch_id, batch_name, upload_source, create_time, update_time, deleted)
        VALUES (#{user.douyinId}, #{user.starMapId}, #{user.batchId}, #{user.batchName},
                COALESCE(#{user.uploadSource}, 'MANUAL'), NOW(), NOW(), #{user.deleted})
    </insert>
    
    <!-- 更新指定表中的用户信息 -->
    <update id="updateInTable">
        UPDATE ${tableName} 
        SET douyin_id = #{user.douyinId}, 
            star_map_id = #{user.starMapId}, 
            update_time = NOW()
        WHERE id = #{user.id}
    </update>
    
    <!-- 软删除指定表中的用户 -->
    <update id="softDeleteInTable">
        UPDATE ${tableName} 
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 动态表名批量插入 -->
    <insert id="batchInsertToTable" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO ${tableName} (douyin_id, star_map_id, douyin_link, batch_id, batch_name, upload_source, create_time, update_time, deleted)
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.douyinId},
                    #{item.starMapId},
                    #{item.douyinLink},
                    #{item.batchId},
                    #{item.batchName},
                    COALESCE(#{item.uploadSource}, 'MANUAL'),
                    COALESCE(#{item.createTime}, NOW()),
                    COALESCE(#{item.updateTime}, NOW()),
                    COALESCE(#{item.deleted}, 0)
                )
                </foreach>
                ON DUPLICATE KEY UPDATE
                star_map_id = VALUES(star_map_id),
                douyin_link = VALUES(douyin_link),
                batch_id = VALUES(batch_id),
                batch_name = VALUES(batch_name),
                upload_source = VALUES(upload_source),
                update_time = NOW()
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 批量插入用户到指定表（仅插入，不更新） -->
    <insert id="batchInsertOnlyToTable" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT IGNORE INTO ${tableName} (douyin_id, star_map_id, douyin_link, batch_id, batch_name, upload_source, create_time, update_time, deleted)
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.douyinId},
                    #{item.starMapId},
                    #{item.douyinLink},
                    #{item.batchId},
                    #{item.batchName},
                    COALESCE(#{item.uploadSource}, 'MANUAL'),
                    COALESCE(#{item.createTime}, NOW()),
                    COALESCE(#{item.updateTime}, NOW()),
                    COALESCE(#{item.deleted}, 0)
                )
                </foreach>
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 批量插入新记录到指定表（总是插入，允许重复） -->
    <insert id="batchInsertNewToTable" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO ${tableName} (douyin_id, star_map_id, batch_id, batch_name, upload_source, create_time, update_time, deleted)
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.douyinId},
                    #{item.starMapId},
                    #{item.batchId},
                    #{item.batchName},
                    COALESCE(#{item.uploadSource}, 'MANUAL'),
                    COALESCE(#{item.createTime}, NOW()),
                    COALESCE(#{item.updateTime}, NOW()),
                    COALESCE(#{item.deleted}, 0)
                )
                </foreach>
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 批量插入或更新用户到指定表（覆盖模式，基于 douyin_id 或 star_map_id 匹配） -->
    <insert id="batchUpsertToTable" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO ${tableName} (douyin_id, star_map_id, batch_id, batch_name, upload_source, create_time, update_time, deleted)
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.douyinId},
                    #{item.starMapId},
                    #{item.batchId},
                    #{item.batchName},
                    COALESCE(#{item.uploadSource}, 'MANUAL'),
                    COALESCE(#{item.createTime}, NOW()),
                    COALESCE(#{item.updateTime}, NOW()),
                    COALESCE(#{item.deleted}, 0)
                )
                </foreach>
                ON DUPLICATE KEY UPDATE
                    star_map_id = VALUES(star_map_id),
                    douyin_id = VALUES(douyin_id),
                    batch_id = VALUES(batch_id),
                    batch_name = VALUES(batch_name),
                    upload_source = VALUES(upload_source),
                    update_time = NOW(),
                    deleted = VALUES(deleted)
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 统计指定表中已存在的抖音ID数量 -->
    <select id="countExistingByDouyinIds" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        WHERE douyin_id IN
        <foreach collection="douyinIds" item="douyinId" open="(" separator="," close=")">
            #{douyinId}
        </foreach>
    </select>

    <!-- 统计指定表中已存在的星图ID数量 -->
    <select id="countExistingByStarMapIds" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        WHERE star_map_id IN
        <foreach collection="starMapIds" item="starMapId" open="(" separator="," close=")">
            #{starMapId}
        </foreach>
    </select>

    <!-- 统计指定表中指定批次的记录数量 -->
    <select id="countByBatchId" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        WHERE batch_id = #{batchId}
    </select>

    <!-- 根据抖音ID列表查询用户记录 -->
    <select id="selectByDouyinIds" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName}
        WHERE douyin_id IN
        <foreach collection="douyinIds" item="douyinId" open="(" separator="," close=")">
            #{douyinId}
        </foreach>
        ORDER BY updated_time DESC
    </select>

    <!-- 兼容性接口 - 使用当前月表 -->
    <!-- 根据抖音ID查询用户 -->
    <select id="selectByDouyinId" parameterType="string" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM oppo_user WHERE douyin_id = #{douyinId} AND deleted = 0
    </select>
    
    <!-- 根据星图ID查询用户 -->
    <select id="selectByStarMapId" parameterType="string" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM oppo_user WHERE star_map_id = #{starMapId} AND deleted = 0
    </select>
    
    <!-- 批量插入用户 -->
    <insert id="batchInsert" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                INSERT INTO oppo_user (douyin_id, star_map_id, create_time, update_time, deleted)
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.douyinId}, 
                    #{item.starMapId}, 
                    COALESCE(#{item.createTime}, NOW()), 
                    COALESCE(#{item.updateTime}, NOW()), 
                    COALESCE(#{item.deleted}, 0)
                )
                </foreach>
                ON DUPLICATE KEY UPDATE
                star_map_id = VALUES(star_map_id),
                update_time = NOW()
            </when>
            <otherwise>
                SELECT 0
            </otherwise>
        </choose>
    </insert>

    <!-- 根据抖音ID查询所有匹配的用户（用于检测重复） -->
    <select id="selectAllByDouyinId" resultType="data.oppodataapi.entity.OppoUser">
        SELECT * FROM ${tableName}
        WHERE douyin_id = #{douyinId}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 获取表中总记录数 -->
    <select id="getTotalRecordCount" resultType="int">
        SELECT COUNT(*) FROM ${tableName}
        WHERE deleted = 0
    </select>

    <!-- 获取唯一抖音ID数量 -->
    <select id="getUniqueDouyinIdCount" resultType="int">
        SELECT COUNT(DISTINCT douyin_id) FROM ${tableName}
        WHERE deleted = 0 AND douyin_id IS NOT NULL AND douyin_id != ''
    </select>

    <!-- 获取唯一星图ID数量 -->
    <select id="getUniqueStarMapIdCount" resultType="int">
        SELECT COUNT(DISTINCT star_map_id) FROM ${tableName}
        WHERE deleted = 0 AND star_map_id IS NOT NULL AND star_map_id != ''
    </select>

</mapper>
