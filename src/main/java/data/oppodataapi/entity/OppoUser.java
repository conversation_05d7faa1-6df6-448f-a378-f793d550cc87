package data.oppodataapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OPPO用户实体类
 */
@Data
@TableName("oppo_user")
public class OppoUser {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 抖音ID
     */
    private String douyinId;
    
    /**
     * 星图ID
     */
    private String starMapId;

    /**
     * 抖音链接
     */
    private String douyinLink;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 上传来源：EXCEL, API, MANUAL
     */
    private String uploadSource;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer deleted;
    
    // 构造函数
    public OppoUser() {}
    
    public OppoUser(String douyinId, String starMapId) {
        this.douyinId = douyinId;
        this.starMapId = starMapId;
        this.deleted = 0;
        this.uploadSource = "MANUAL";
    }

    public OppoUser(String douyinId, String starMapId, String batchId, String uploadSource) {
        this.douyinId = douyinId;
        this.starMapId = starMapId;
        this.batchId = batchId;
        this.uploadSource = uploadSource;
        this.deleted = 0;
    }

    public OppoUser(String douyinId, String starMapId, String douyinLink, String batchId, String uploadSource) {
        this.douyinId = douyinId;
        this.starMapId = starMapId;
        this.douyinLink = douyinLink;
        this.batchId = batchId;
        this.uploadSource = uploadSource;
        this.deleted = 0;
    }
    
    // Getter 和 Setter 方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getDouyinId() {
        return douyinId;
    }
    
    public void setDouyinId(String douyinId) {
        this.douyinId = douyinId;
    }
    
    public String getStarMapId() {
        return starMapId;
    }
    
    public void setStarMapId(String starMapId) {
        this.starMapId = starMapId;
    }

    public String getDouyinLink() {
        return douyinLink;
    }

    public void setDouyinLink(String douyinLink) {
        this.douyinLink = douyinLink;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Integer getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
