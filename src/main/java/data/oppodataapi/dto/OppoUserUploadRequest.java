package data.oppodataapi.dto;

import lombok.Data;

import java.util.List;

/**
 * OPPO用户上传请求DTO
 */
@Data
public class OppoUserUploadRequest {
    
    /**
     * 认证令牌
     */
    private String token;
    
    /**
     * 用户数据列表
     */
    private List<OppoUserData> users;
    
    /**
     * 是否覆盖已存在的数据
     */
    private Boolean overwrite = false;
    
    /**
     * 批次大小（可选，默认100）
     */
    private Integer batchSize = 100;
    
    /**
     * 用户数据内部类
     */
    @Data
    public static class OppoUserData {
        
        /**
         * 抖音ID
         */
        private String douyinId;
        
        /**
         * 星图ID
         */
        private String starMapId;

        /**
         * 抖音链接
         */
        private String douyinLink;

        // 构造函数
        public OppoUserData() {}
        
        public OppoUserData(String douyinId, String starMapId) {
            this.douyinId = douyinId;
            this.starMapId = starMapId;
        }

        public OppoUserData(String douyinId, String starMapId, String douyinLink) {
            this.douyinId = douyinId;
            this.starMapId = starMapId;
            this.douyinLink = douyinLink;
        }
        
        // Getter 和 Setter 方法
        public String getDouyinId() {
            return douyinId;
        }
        
        public void setDouyinId(String douyinId) {
            this.douyinId = douyinId;
        }
        
        public String getStarMapId() {
            return starMapId;
        }
        
        public void setStarMapId(String starMapId) {
            this.starMapId = starMapId;
        }

        public String getDouyinLink() {
            return douyinLink;
        }

        public void setDouyinLink(String douyinLink) {
            this.douyinLink = douyinLink;
        }
    }
    
    // 构造函数
    public OppoUserUploadRequest() {}
    
    public OppoUserUploadRequest(String token, List<OppoUserData> users) {
        this.token = token;
        this.users = users;
    }
    
    // Getter 和 Setter 方法
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public List<OppoUserData> getUsers() {
        return users;
    }
    
    public void setUsers(List<OppoUserData> users) {
        this.users = users;
    }
    
    public Boolean getOverwrite() {
        return overwrite;
    }
    
    public void setOverwrite(Boolean overwrite) {
        this.overwrite = overwrite;
    }
    
    public Integer getBatchSize() {
        return batchSize;
    }
    
    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }
}
