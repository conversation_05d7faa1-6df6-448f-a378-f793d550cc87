package data.oppodataapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分页查询请求DTO
 */
@Data
public class PageQueryRequest {

    /**
     * 用户查询条件对象（每个实例只查询一条数据）
     */
    @Data
    public static class UserCondition {
        /**
         * 用户ID（支持抖音ID或星图ID）
         */
        private String userId;

        /**
         * 抖音ID
         */
        private String douyinId;

        /**
         * 星图ID
         */
        private String starMapId;

        /**
         * 抖音链接
         */
        private String douyinLink;

        /**
         * 构造函数
         */
        public UserCondition() {}

        /**
         * 构造函数 - 通过用户ID
         */
        public UserCondition(String userId) {
            this.userId = userId;
        }

        /**
         * 构造函数 - 通过抖音ID
         */
        public UserCondition(String douyinId, String starMapId) {
            this.douyinId = douyinId;
            this.starMapId = starMapId;
        }

        /**
         * 构造函数 - 完整参数
         */
        public UserCondition(String userId, String douyinId, String starMapId, String douyinLink) {
            this.userId = userId;
            this.douyinId = douyinId;
            this.starMapId = starMapId;
            this.douyinLink = douyinLink;
        }

        /**
         * 获取有效的查询ID（优先级：douyinId > starMapId > userId）
         * @return 有效的查询ID
         */
        public String getEffectiveId() {
            if (StringUtils.hasText(douyinId)) {
                return douyinId.trim();
            }
            if (StringUtils.hasText(starMapId)) {
                return starMapId.trim();
            }
            if (StringUtils.hasText(userId)) {
                return userId.trim();
            }
            return null;
        }

        /**
         * 获取查询类型
         * @return 查询类型描述
         */
        public String getQueryType() {
            if (StringUtils.hasText(douyinId)) {
                return "douyinId";
            }
            if (StringUtils.hasText(starMapId)) {
                return "starMapId";
            }
            if (StringUtils.hasText(userId)) {
                return "userId";
            }
            if (StringUtils.hasText(douyinLink)) {
                return "douyinLink";
            }
            return "none";
        }

        /**
         * 获取查询字段名
         * @return 数据库字段名
         */
        public String getQueryFieldName() {
            if (StringUtils.hasText(douyinId)) {
                return "douyin_id";
            }
            if (StringUtils.hasText(starMapId)) {
                return "star_map_id";
            }
            if (StringUtils.hasText(userId)) {
                return "user_id";
            }
            if (StringUtils.hasText(douyinLink)) {
                return "douyin_link";
            }
            return null;
        }

        /**
         * 获取查询值
         * @return 查询值
         */
        public String getQueryValue() {
            if (StringUtils.hasText(douyinId)) {
                return douyinId.trim();
            }
            if (StringUtils.hasText(starMapId)) {
                return starMapId.trim();
            }
            if (StringUtils.hasText(userId)) {
                return userId.trim();
            }
            if (StringUtils.hasText(douyinLink)) {
                return douyinLink.trim();
            }
            return null;
        }

        /**
         * 检查是否有有效的查询条件
         * @return true如果有有效的查询条件
         */
        public boolean hasValidCondition() {
            return StringUtils.hasText(userId) ||
                   StringUtils.hasText(douyinId) ||
                   StringUtils.hasText(starMapId) ||
                   StringUtils.hasText(douyinLink);
        }

        /**
         * 检查是否为空条件
         * @return true如果没有任何查询条件
         */
        public boolean isEmpty() {
            return !hasValidCondition();
        }

        /**
         * 验证条件的有效性
         * @return 验证结果
         */
        public String validate() {
            if (!hasValidCondition()) {
                return "至少需要提供一个查询条件（userId、douyinId、starMapId 或 douyinLink）";
            }

            // 检查是否提供了多个ID（建议只提供一个）
            int idCount = 0;
            if (StringUtils.hasText(userId)) idCount++;
            if (StringUtils.hasText(douyinId)) idCount++;
            if (StringUtils.hasText(starMapId)) idCount++;
            if (StringUtils.hasText(douyinLink)) idCount++;

            if (idCount > 1) {
                return "建议每个 UserCondition 只提供一个查询条件，当前提供了 " + idCount + " 个";
            }

            return null; // 验证通过
        }

        @Override
        public String toString() {
            return "UserCondition{" +
                    "userId='" + userId + '\'' +
                    ", douyinId='" + douyinId + '\'' +
                    ", starMapId='" + starMapId + '\'' +
                    ", douyinLink='" + douyinLink + '\'' +
                    ", queryType='" + getQueryType() + '\'' +
                    ", effectiveId='" + getEffectiveId() + '\'' +
                    '}';
        }
    }

    /**
     * 认证令牌
     */
    private String token;

    /**
     * 页码（从1开始）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 100;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 用户查询条件列表（推荐使用）
     * 每个 UserCondition 只查询一条数据
     */
    private List<UserCondition> userCondition;

    /**
     * 用户ID列表（逗号分隔的字符串，可选）
     * @deprecated 建议使用 userCondition.userIds
     * 兼容原有接口
     */
    @Deprecated
    private String userIds;

    /**
     * 用户ID列表（数组形式，支持抖音ID和星图ID混合）
     * @deprecated 建议使用 userCondition.userIdList
     */
    @Deprecated
    private List<String> userIdList;

    /**
     * 抖音ID列表
     * @deprecated 建议使用 userCondition.douyinIds
     */
    @Deprecated
    private List<String> douyinIds;

    /**
     * 星图ID列表
     * @deprecated 建议使用 userCondition.starMapIds
     */
    @Deprecated
    private List<String> starMapIds;

    /**
     * 开始上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startUploadTime;

    /**
     * 结束上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endUploadTime;

    /**
     * 指定查询的周表名（可选，如：oppo_data_2025_27）
     * 如果不指定，则查询当前周表
     */
    private String weekTableName;

    /**
     * 平台标识（可选）
     */
    private String platform;

    /**
     * 搜索关键词（可选）
     */
    private String keyword;

    /**
     * 排序字段（默认按update_time降序）
     */
    private String orderBy = "updated_time";

    /**
     * 排序方向（ASC/DESC，默认DESC）
     */
    private String orderDirection = "DESC";

    // 构造函数
    public PageQueryRequest() {}

    public PageQueryRequest(String token, Integer pageNum, Integer pageSize) {
        this.token = token;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    /**
     * 获取有效的用户查询条件列表（兼容新旧两种方式）
     * @return 用户查询条件列表
     */
    public List<UserCondition> getEffectiveUserConditions() {
        List<UserCondition> conditions = new ArrayList<>();

        // 如果有新的userCondition列表，优先使用
        if (userCondition != null && !userCondition.isEmpty()) {
            // 过滤掉无效的条件
            conditions.addAll(userCondition.stream()
                .filter(UserCondition::hasValidCondition)
                .collect(Collectors.toList()));
        }

        // 如果新的条件列表为空，从旧的字段构建UserCondition对象
        if (conditions.isEmpty()) {
            // 从旧的userIds字段构建
            if (StringUtils.hasText(this.userIds)) {
                String[] ids = this.userIds.split(",");
                for (String id : ids) {
                    if (StringUtils.hasText(id.trim())) {
                        conditions.add(new UserCondition(id.trim()));
                    }
                }
            }

            // 从旧的userIdList字段构建
            if (this.userIdList != null && !this.userIdList.isEmpty()) {
                for (String id : this.userIdList) {
                    if (StringUtils.hasText(id)) {
                        conditions.add(new UserCondition(id.trim()));
                    }
                }
            }

            // 从旧的douyinIds字段构建
            if (this.douyinIds != null && !this.douyinIds.isEmpty()) {
                for (String id : this.douyinIds) {
                    if (StringUtils.hasText(id)) {
                        UserCondition condition = new UserCondition();
                        condition.setDouyinId(id.trim());
                        conditions.add(condition);
                    }
                }
            }

            // 从旧的starMapIds字段构建
            if (this.starMapIds != null && !this.starMapIds.isEmpty()) {
                for (String id : this.starMapIds) {
                    if (StringUtils.hasText(id)) {
                        UserCondition condition = new UserCondition();
                        condition.setStarMapId(id.trim());
                        conditions.add(condition);
                    }
                }
            }
        }

        return conditions;
    }

    /**
     * 检查是否有任何用户查询条件
     * @return true如果有任何用户查询条件
     */
    public boolean hasAnyUserCondition() {
        List<UserCondition> conditions = getEffectiveUserConditions();
        return !conditions.isEmpty();
    }

    /**
     * 获取用户查询条件的数量
     * @return 用户查询条件数量
     */
    public int getUserConditionCount() {
        List<UserCondition> conditions = getEffectiveUserConditions();
        return conditions.size();
    }

    /**
     * 参数验证和标准化
     */
    public void validate() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }

        if (pageSize == null || pageSize < 1) {
            pageSize = 100;
        } else if (pageSize > 1000) {
            pageSize = 1000;
        }

        // 验证时间范围
        if (startUploadTime != null && endUploadTime != null) {
            if (startUploadTime.isAfter(endUploadTime)) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
        }

        // 验证用户查询条件
        if (hasAnyUserCondition()) {
            List<UserCondition> conditions = getEffectiveUserConditions();

            // 检查条件数量限制
            if (conditions.size() > 100) {
                throw new IllegalArgumentException("用户查询条件数量不能超过100个，当前: " + conditions.size());
            }

            // 验证每个条件的有效性
            for (int i = 0; i < conditions.size(); i++) {
                UserCondition condition = conditions.get(i);
                String validationError = condition.validate();
                if (validationError != null) {
                    throw new IllegalArgumentException("第 " + (i + 1) + " 个用户查询条件无效: " + validationError);
                }
            }
        }
    }

    @Override
    public String toString() {
        return "PageQueryRequest{" +
                "token='" + (token != null ? "***" : null) + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", batchId='" + batchId + '\'' +
                ", userCondition=" + userCondition +
                ", userIds='" + userIds + '\'' +
                ", userIdList=" + (userIdList != null ? userIdList.size() + " items" : null) +
                ", douyinIds=" + (douyinIds != null ? douyinIds.size() + " items" : null) +
                ", starMapIds=" + (starMapIds != null ? starMapIds.size() + " items" : null) +
                ", startUploadTime=" + startUploadTime +
                ", endUploadTime=" + endUploadTime +
                ", weekTableName='" + weekTableName + '\'' +
                ", platform='" + platform + '\'' +
                ", keyword='" + keyword + '\'' +
                ", orderBy='" + orderBy + '\'' +
                ", orderDirection='" + orderDirection + '\'' +
                '}';
    }
}
