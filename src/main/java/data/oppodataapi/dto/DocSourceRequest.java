package data.oppodataapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 文档源查询请求DTO
 */

@Data
public class DocSourceRequest {
    
    /**
     * 返回记录数量
     */
    private Integer top;
    
    /**
     * 平台ID列表
     */
    @JsonProperty("platformIds")
    private List<Integer> platformIds;
    
    /**
     * 额外条件
     */
    @JsonProperty("extraCondition")
    private List<Object> extraCondition;
    
    /**
     * 索引类型
     */
    @JsonProperty("indexType")
    private String indexType;
    
    /**
     * 主体ID列表
     */
    @JsonProperty("mainPartyId")
    private List<Integer> mainPartyId;
    
    /**
     * 行数据
     */
    @JsonProperty("rowVo")
    private List<Object> rowVo;
    
    /**
     * 列数据
     */
    @JsonProperty("columnVo")
    private Object columnVo;
    
    /**
     * 指标数据
     */
    @JsonProperty("metricsVo")
    private List<Object> metricsVo;
    
    /**
     * 路径
     */
    private String path;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 下载数量
     */
    @JsonProperty("downloadCount")
    private List<String> downloadCount;
    
    /**
     * 扁平化源数据
     */
    @JsonProperty("flatSource")
    private Boolean flatSource;
    
    /**
     * 页面大小
     */
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    /**
     * 页码
     */
    @JsonProperty("pageNum")
    private Integer pageNum;
    
    /**
     * 数组扁平化源数据
     */
    @JsonProperty("arrayFlatSource")
    private Boolean arrayFlatSource;
    
    /**
     * 高亮显示
     */
    @JsonProperty("highLight")
    private Boolean highLight;

    String token;

    private String startDate;
    private String endDate;
    
    // 构造函数
    public DocSourceRequest() {}
    
    // Getter 和 Setter 方法
    public Integer getTop() {
        return top;
    }
    
    public void setTop(Integer top) {
        this.top = top;
    }
    
    public List<Integer> getPlatformIds() {
        return platformIds;
    }
    
    public void setPlatformIds(List<Integer> platformIds) {
        this.platformIds = platformIds;
    }
    
    public List<Object> getExtraCondition() {
        return extraCondition;
    }
    
    public void setExtraCondition(List<Object> extraCondition) {
        this.extraCondition = extraCondition;
    }
    
    public String getIndexType() {
        return indexType;
    }
    
    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }
    
    public List<Integer> getMainPartyId() {
        return mainPartyId;
    }
    
    public void setMainPartyId(List<Integer> mainPartyId) {
        this.mainPartyId = mainPartyId;
    }
    
    public List<Object> getRowVo() {
        return rowVo;
    }
    
    public void setRowVo(List<Object> rowVo) {
        this.rowVo = rowVo;
    }
    
    public Object getColumnVo() {
        return columnVo;
    }
    
    public void setColumnVo(Object columnVo) {
        this.columnVo = columnVo;
    }
    
    public List<Object> getMetricsVo() {
        return metricsVo;
    }
    
    public void setMetricsVo(List<Object> metricsVo) {
        this.metricsVo = metricsVo;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public List<String> getDownloadCount() {
        return downloadCount;
    }
    
    public void setDownloadCount(List<String> downloadCount) {
        this.downloadCount = downloadCount;
    }
    
    public Boolean getFlatSource() {
        return flatSource;
    }
    
    public void setFlatSource(Boolean flatSource) {
        this.flatSource = flatSource;
    }
    
    public Integer getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public Integer getPageNum() {
        return pageNum;
    }
    
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
    
    public Boolean getArrayFlatSource() {
        return arrayFlatSource;
    }
    
    public void setArrayFlatSource(Boolean arrayFlatSource) {
        this.arrayFlatSource = arrayFlatSource;
    }
    
    public Boolean getHighLight() {
        return highLight;
    }
    
    public void setHighLight(Boolean highLight) {
        this.highLight = highLight;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getEndDate() {
        return endDate;
    }
}
