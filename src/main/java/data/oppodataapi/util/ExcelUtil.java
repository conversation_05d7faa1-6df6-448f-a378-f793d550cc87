package data.oppodataapi.util;

import data.oppodataapi.dto.OppoUserUploadRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel 处理工具类
 */
public class ExcelUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);
    
    /**
     * 从 Excel 文件中读取用户数据
     * @param file Excel 文件
     * @return 用户数据列表
     * @throws IOException 文件读取异常
     */
    public static List<OppoUserUploadRequest.OppoUserData> readUsersFromExcel(MultipartFile file) throws IOException {
        List<OppoUserUploadRequest.OppoUserData> userList = new ArrayList<>();
        
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel 文件不能为空");
        }
        
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new IllegalArgumentException("文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件");
        }
        
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(inputStream, fileName);
            
            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Excel 文件中没有找到工作表");
            }
            
            logger.info("开始读取 Excel 文件: {}, 工作表: {}, 总行数: {}", 
                       fileName, sheet.getSheetName(), sheet.getLastRowNum() + 1);
            
            // 读取数据行（跳过标题行）
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }
                
                try {
                    OppoUserUploadRequest.OppoUserData userData = readUserDataFromRow(row, rowIndex);
                    if (userData != null) {
                        userList.add(userData);
                    }
                } catch (Exception e) {
                    logger.warn("读取第 {} 行数据失败: {}", rowIndex + 1, e.getMessage());
                }
            }
            
            workbook.close();
            
        } catch (IOException e) {
            logger.error("读取 Excel 文件失败: {}", e.getMessage(), e);
            throw new IOException("读取 Excel 文件失败: " + e.getMessage(), e);
        }
        
        logger.info("Excel 文件读取完成，共读取到 {} 条有效用户数据", userList.size());
        return userList;
    }
    
    /**
     * 创建 Workbook 对象
     * @param inputStream 输入流
     * @param fileName 文件名
     * @return Workbook 对象
     * @throws IOException 创建异常
     */
    private static Workbook createWorkbook(InputStream inputStream, String fileName) throws IOException {
        try {
            // 使用BufferedInputStream来提高读取性能并支持mark/reset
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);

            // 标记流的位置，以便在失败时重置
            bufferedInputStream.mark(1024);

            if (fileName.toLowerCase().endsWith(".xlsx")) {
                try {
                    return new XSSFWorkbook(bufferedInputStream);
                } catch (Exception e) {
                    logger.warn("使用XLSX格式读取失败，尝试其他方式: {}", e.getMessage());
                    // 重置流位置
                    bufferedInputStream.reset();
                    // 可能是旧版本的Excel文件，尝试用HSSFWorkbook读取
                    return new HSSFWorkbook(bufferedInputStream);
                }
            } else if (fileName.toLowerCase().endsWith(".xls")) {
                try {
                    return new HSSFWorkbook(bufferedInputStream);
                } catch (Exception e) {
                    logger.warn("使用XLS格式读取失败，尝试其他方式: {}", e.getMessage());
                    // 重置流位置
                    bufferedInputStream.reset();
                    // 可能是新版本的Excel文件，尝试用XSSFWorkbook读取
                    return new XSSFWorkbook(bufferedInputStream);
                }
            } else {
                throw new IllegalArgumentException("不支持的文件格式: " + fileName);
            }
        } catch (Exception e) {
            logger.error("创建Workbook失败，文件名: {}, 错误: {}", fileName, e.getMessage(), e);

            // 检查是否是字符编码问题
            if (e.getMessage() != null && e.getMessage().contains("MalformedInputException")) {
                throw new IOException("Excel文件包含不支持的字符编码，请确保文件是标准的Excel格式", e);
            }

            throw new IOException("Excel文件格式错误或文件损坏: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从行中读取用户数据
     * @param row Excel 行
     * @param rowIndex 行索引
     * @return 用户数据
     */
    private static OppoUserUploadRequest.OppoUserData readUserDataFromRow(Row row, int rowIndex) {
        try {
            // 读取抖音ID（第一列，索引0）
            String douyinId = getCellValueAsString(row.getCell(0));

            // 读取星图ID（第二列，索引1）
            String starMapId = getCellValueAsString(row.getCell(1));

            // 读取抖音链接（第三列，索引2）
            String douyinLink = getCellValueAsString(row.getCell(2));

            // 至少需要有一个ID不为空
            if (!StringUtils.hasText(douyinId) && !StringUtils.hasText(starMapId)) {
                logger.debug("第 {} 行数据为空，跳过", rowIndex + 1);
                return null;
            }

            OppoUserUploadRequest.OppoUserData userData = new OppoUserUploadRequest.OppoUserData();
            userData.setDouyinId(StringUtils.hasText(douyinId) ? douyinId.trim() : null);
            userData.setStarMapId(StringUtils.hasText(starMapId) ? starMapId.trim() : null);
            userData.setDouyinLink(StringUtils.hasText(douyinLink) ? douyinLink.trim() : null);

            logger.debug("读取第 {} 行数据: douyinId={}, starMapId={}, douyinLink={}",
                        rowIndex + 1, userData.getDouyinId(), userData.getStarMapId(), userData.getDouyinLink());
            
            return userData;
            
        } catch (Exception e) {
            logger.error("解析第 {} 行数据失败: {}", rowIndex + 1, e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取单元格的字符串值
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    String stringValue = cell.getStringCellValue();
                    return safeReadString(stringValue);

                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        // 处理数字，避免科学计数法
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == Math.floor(numericValue)) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }

                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());

                case FORMULA:
                    try {
                        // 尝试获取计算后的字符串值
                        String formulaStringValue = cell.getStringCellValue();
                        return safeReadString(formulaStringValue);
                    } catch (Exception e) {
                        try {
                            // 尝试获取计算后的数值
                            double numericValue = cell.getNumericCellValue();
                            if (Double.isNaN(numericValue) || Double.isInfinite(numericValue)) {
                                return null;
                            }
                            if (numericValue == Math.floor(numericValue)) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        } catch (Exception ex) {
                            // 返回公式本身
                            try {
                                return cell.getCellFormula();
                            } catch (Exception formulaEx) {
                                logger.warn("无法读取公式单元格: {}", formulaEx.getMessage());
                                return null;
                            }
                        }
                    }

                case BLANK:
                case _NONE:
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.warn("读取单元格值时发生错误，行: {}, 列: {}, 错误: {}",
                       cell.getRowIndex(), cell.getColumnIndex(), e.getMessage());
            return null;
        }
    }

    /**
     * 安全地读取字符串，处理编码问题
     * @param value 原始字符串
     * @return 处理后的字符串
     */
    private static String safeReadString(String value) {
        if (value == null) {
            return null;
        }

        try {
            // 移除BOM字符
            if (value.startsWith("\uFEFF")) {
                value = value.substring(1);
            }

            // 移除控制字符，但保留常见的空白字符
            value = value.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");

            // 处理可能的编码问题
            value = value.trim();

            return value.isEmpty() ? null : value;

        } catch (Exception e) {
            logger.warn("处理字符串时发生错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证 Excel 文件格式
     * @param file 文件
     * @return 是否为有效的 Excel 文件
     */
    public static boolean isValidExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            logger.warn("文件为空或不存在");
            return false;
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            logger.warn("文件名为空");
            return false;
        }

        // 检查文件扩展名
        String lowerFileName = fileName.toLowerCase();
        if (!lowerFileName.endsWith(".xlsx") && !lowerFileName.endsWith(".xls")) {
            logger.warn("不支持的文件格式: {}", fileName);
            return false;
        }

        // 检查文件大小（不能超过10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            logger.warn("Excel文件过大: {} bytes", file.getSize());
            return false;
        }

        // 尝试读取文件头来验证是否真的是Excel文件
        try (InputStream inputStream = file.getInputStream()) {
            byte[] header = new byte[8];
            int bytesRead = inputStream.read(header);

            if (bytesRead < 4) {
                logger.warn("文件内容不足，无法验证格式");
                return false;
            }

            // 检查Excel文件的魔数
            if (lowerFileName.endsWith(".xlsx")) {
                // XLSX文件是ZIP格式，检查ZIP魔数 (PK)
                boolean isZip = header[0] == 0x50 && header[1] == 0x4B;
                if (!isZip) {
                    logger.warn("XLSX文件格式验证失败");
                }
                return isZip;
            } else {
                // XLS文件检查OLE魔数
                boolean isOle = header[0] == (byte)0xD0 && header[1] == (byte)0xCF &&
                               header[2] == 0x11 && header[3] == (byte)0xE0;
                if (!isOle) {
                    logger.warn("XLS文件格式验证失败");
                }
                return isOle;
            }
        } catch (Exception e) {
            logger.warn("验证Excel文件时发生错误: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取 Excel 文件的预期格式说明
     * @return 格式说明
     */
    public static String getExcelFormatDescription() {
        return "Excel 文件格式要求：\n" +
               "1. 文件格式：.xlsx 或 .xls\n" +
               "2. 第一行为标题行（会被跳过）\n" +
               "3. 第一列：抖音ID\n" +
               "4. 第二列：星图ID\n" +
               "5. 每行至少需要有一个ID不为空\n" +
               "6. 建议标题行格式：抖音ID | 星图ID";
    }

    /**
     * 生成 Excel 模板文件
     * @param response HTTP 响应
     * @throws IOException 文件生成异常
     */
    public static void generateExcelTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=oppo_user_template.xlsx");

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户数据");

            // 创建标题行
            Row headerRow = sheet.createRow(0);

            // 创建单元格样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            // 设置标题
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("抖音ID");
            cell1.setCellStyle(headerStyle);

            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("星图ID");
            cell2.setCellStyle(headerStyle);

            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("主页长链接");
            cell3.setCellStyle(headerStyle);

            // 添加示例数据
            Row exampleRow1 = sheet.createRow(1);
            exampleRow1.createCell(0).setCellValue("example_douyin_001");
            exampleRow1.createCell(1).setCellValue("example_starmap_001");
            exampleRow1.createCell(2).setCellValue("https://www.douyin.com/user/MS4wLjABAAAAJxxxxxxxx");

            Row exampleRow2 = sheet.createRow(2);
            exampleRow2.createCell(0).setCellValue("example_douyin_002");
            exampleRow2.createCell(1).setCellValue("example_starmap_002");
            exampleRow2.createCell(2).setCellValue("https://www.douyin.com/user/xxxxxxxxxxxxxx");


            // 自动调整列宽
            sheet.autoSizeColumn(0);
            sheet.autoSizeColumn(1);

            // 设置列宽（如果自动调整不够）
            sheet.setColumnWidth(0, 20 * 256); // 20个字符宽度
            sheet.setColumnWidth(1, 20 * 256); // 20个字符宽度

            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }

            logger.info("Excel 模板文件生成成功");

        } catch (IOException e) {
            logger.error("生成 Excel 模板文件失败: {}", e.getMessage(), e);
            throw new IOException("生成 Excel 模板文件失败: " + e.getMessage(), e);
        }
    }
}
