package data.oppodataapi.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import data.oppodataapi.dto.DocSourceRequest;
import data.oppodataapi.dto.QueryRequest;
import data.oppodataapi.dto.PageQueryRequest;
import data.oppodataapi.dto.PageResponse;
import data.oppodataapi.entity.OppoData;
import data.oppodataapi.mapper.OppoDataMapper;
import data.oppodataapi.mapper.OppoUserMapper;
import data.oppodataapi.util.WeeklyTableUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据中心服务类
 */
@Service
public class OppoDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(OppoDataService.class);
    
    @Value("${datacenter.api.base-url:https://dag-api.tarsocial.com}")
    private String baseUrl;
    
    @Autowired
    private TokenService tokenService;
    //这个token 从数据库中获取
    
    @Value("${datacenter.api.organization-id}")
    private String organizationId;

    private final RestTemplate restTemplate;

    @Autowired
    private TokenValidationService tokenValidationService;

    @Autowired
    private OppoDataMapper oppoDataMapper;

    // 缓存最新有数据的表名，避免频繁查询
    private volatile String cachedLatestTableWithData = null;
    private volatile long lastCacheTime = 0;
    private static final long CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

    public OppoDataService() {
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 查询数据中心数据
     * @param request 查询请求
     * @return 查询结果
     */
    public Object queryDataCenter(QueryRequest request) {
        String url = baseUrl + "/dc/dataCenter/query";
        
        HttpHeaders headers = createHeaders();
        HttpEntity<QueryRequest> entity = new HttpEntity<>(request, headers);
        
        try {
            logger.info("调用数据中心查询接口: {}", url);
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.debug("请求参数: {}", request);
            
            ResponseEntity<Object> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                Object.class
            );
            
            logger.info("数据中心查询接口调用成功");
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用数据中心查询接口失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用数据中心查询接口失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询文档源数据
     * @param request 文档源查询请求
     * @return 查询结果
     */
    public Object queryDocSource(DocSourceRequest request) {
        String url = baseUrl + "/dc/extend/doc-source";
        HttpHeaders headers  = createHeaders();
        HttpEntity<DocSourceRequest> entity = new HttpEntity<>(request, headers);
        
        try {
            logger.info("调用文档源查询接口: {}", url);
             logger.debug("请求参数: {}", request);

            ResponseEntity<Object> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                Object.class
            );
            List<String> tid = response.getHeaders().get("request-id");
            logger.info("请求ID: {}", tid.get(0));
            logger.info("文档源查询接口调用成功");
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用文档源查询接口失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用文档源查询接口失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建请求头
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        // 从数据库获取Token
        String token = tokenService.getDataCenterToken();
        headers.set("Authorization", "Bearer " + token);
        headers.set("organizationid", organizationId);
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        return headers;
    }

    /**
     * 创建请求头
     * @return HTTP请求头
     */
    private HttpHeaders createTokenHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        headers.set("Authorization", "Bearer " + token);
        headers.set("organizationid", organizationId);
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        return headers;
    }



    /**
     * 将查询结果转换为OppoData列表
     * @param result 查询结果
     * @return OppoData列表
     */
    @SuppressWarnings("unchecked")
    public List<OppoData> convertToOppoDataList(Object result) {
        List<OppoData> oppoDataList = new ArrayList<>();

        if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            if (resultMap.containsKey("data") && resultMap.get("data") != null) {
                List<Map<String, Object>> dataList = List.of();
                if (resultMap.containsKey("data")) {
                    Object data = resultMap.get("data");
                    // 使用instanceof分情况处理，并添加泛型类型检查
                    if (data instanceof List) {
                        // 强制转换前先检查列表元素是否为Map类型
                        List<?> rawList = (List<?>) data;
                        if (rawList.stream().allMatch(item -> item instanceof Map)) {
                            // 安全转换为List<Map<String, Object>>
                            dataList = (List<Map<String, Object>>) (List<?>) rawList;
                        } else {
                            logger.error("列表中存在非Map类型的数据");
                        }
                    } else if (data instanceof Map) {
                        // 单条数据处理逻辑
                        @SuppressWarnings("unchecked")
                        Map<String, Object> singleData = (Map<String, Object>) data;
                        dataList = Collections.singletonList(singleData);
                    } else {
                        logger.error("data字段类型错误，期望List或Map");
                    }
                } else {
                    logger.error("缺少data字段");
                }
                if (dataList.isEmpty()) {
                    logger.info("没有查询到数据");
                    return Collections.emptyList();
                }

                Map<String, Object> stringObjectMap = dataList.getFirst();
                ArrayList rows = (ArrayList) stringObjectMap.get("rows");

                rows.forEach(item -> {
                    Map<String, Object> sourceMap = (Map<String, Object>) ((LinkedHashMap) item).get("source");

                    if (sourceMap == null) {
                        logger.warn("source为空，跳过此条记录");
                        return;
                    }

                    OppoData oppoData = new OppoData();

                    // 从source中获取基本信息
                    if (sourceMap.containsKey("tkw_nickname")) {
                        oppoData.setUserNickname(String.valueOf(sourceMap.get("tkw_nickname")));
                    }
                    if (sourceMap.containsKey("kw_userId")) {
                        oppoData.setUserId(String.valueOf(sourceMap.get("kw_userId")));
                    }
                    if (sourceMap.containsKey("kw_platformUserId")) {
                        oppoData.setDouyinId(String.valueOf(sourceMap.get("kw_platformUserId")));
                    }
                    if (sourceMap.containsKey("kw_kolId")) {
                        oppoData.setStarMapId(String.valueOf(sourceMap.get("kw_kolId")));
                    }
                    if (sourceMap.containsKey("kw_avatar")) {
                        oppoData.setUserAvatar(String.valueOf(sourceMap.get("kw_avatar")));
                    }
                    if (sourceMap.containsKey("tx_description")) {
                        oppoData.setTalentDescription(String.valueOf(sourceMap.get("tx_description")));
                    }

                    // 链接信息
                    if (sourceMap.containsKey("kw_kolId")) {
                        oppoData.setStarMapLink("https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/" + sourceMap.get("kw_kolId"));
                    }
                    if (sourceMap.containsKey("kw_secUid")) {
                        oppoData.setDouyinLink("https://www.douyin.com/user/" + sourceMap.get("kw_secUid"));
                    }

                    // 粉丝数
                    if (sourceMap.containsKey("long_followersCount")) {
                        oppoData.setFollowersCount(String.valueOf(sourceMap.get("long_followersCount")));
                    }

                    // MCN信息
                    if (sourceMap.containsKey("object_star.kw_mcnName")) {
                        oppoData.setMcn(String.valueOf(sourceMap.get("object_star.kw_mcnName")));
                    }

                    // 价格信息 - 使用扁平化的键
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price1_20")) {
                        oppoData.setShortVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price1_20")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price20_60")) {
                        oppoData.setLongVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price20_60")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price60")) {
                        oppoData.setExtraLongVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price60")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_priceImageArticle")) {
                        oppoData.setImageArticlePrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_priceImageArticle")));
                    }

                    // 预期CPM和CPE
                    if (sourceMap.containsKey("object_star.double_expectedStarCpm20_60")) {
                        oppoData.setExpectedCpm20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpm20_60")));
                    }
                    if (sourceMap.containsKey("object_star.double_expectedStarCpe20_60")) {
                        oppoData.setExpectedCpe20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpe20_60")));
                    }
                    if (sourceMap.containsKey("object_star.long_expectedPlayNum")) {
                        oppoData.setExpectedPlayCount20To60Video(String.valueOf(sourceMap.get("object_star.long_expectedPlayNum")));
                    }

                    // 爆文率
                    if (sourceMap.containsKey("object_star.double_burstRate")) {
                        oppoData.setViralRate(String.valueOf(sourceMap.get("object_star.double_burstRate")));
                    }

                    // 达人身份标签
                    if (sourceMap.containsKey("object_star.kw_statusTag")) {
                        oppoData.setTalentStatusTag(String.valueOf(sourceMap.get("object_star.kw_statusTag")));
                    }
                    if (sourceMap.containsKey("object_star.kw_socialStatusTag")) {
                        oppoData.setTalentSocialStatusTag(String.valueOf(sourceMap.get("object_star.kw_socialStatusTag")));
                    }
                    if (sourceMap.containsKey("kw_tags")) {
                        oppoData.setTalentType(String.valueOf(sourceMap.get("kw_tags")));
                    }

                    // 内容主题
                    if (sourceMap.containsKey("object_star.kw_contentTheme")) {
                        oppoData.setContentTheme(String.valueOf(sourceMap.get("object_star.kw_contentTheme")));
                    }
                    if (sourceMap.containsKey("kw_industryTags")) {
                        oppoData.setIndustryTags(String.valueOf(sourceMap.get("kw_industryTags")));
                    }

                    // 内容一级标签_商单
                    if (sourceMap.containsKey("object_star.flattened_videoTouch.video_touch")) {
                        oppoData.setContentFirstLevelTagForCommercial(String.valueOf(sourceMap.get("object_star.flattened_videoTouch.video_touch")));
                    }

                    // 月连接用户数及相关数据
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_connect.long_count")) {
                        oppoData.setMonthlyConnectedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_connect.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_connect.double_relative_ratio")) {
                        oppoData.setMonthlyConnectedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_connect.double_relative_ratio")));
                    }

                    // 月了解用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_understand.long_count")) {
                        oppoData.setMonthlyUnderstoodUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_understand.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_understand.double_relative_ratio")) {
                        oppoData.setMonthlyUnderstoodUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_understand.double_relative_ratio")));
                    }

                    // 月兴趣用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_interest.long_count")) {
                        oppoData.setMonthlyInterestedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_interest.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_interest.double_relative_ratio")) {
                        oppoData.setMonthlyInterestedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_interest.double_relative_ratio")));
                    }

                    // 月喜欢用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_like.long_count")) {
                        oppoData.setMonthlyLikedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_like.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_like.double_relative_ratio")) {
                        oppoData.setMonthlyLikedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_like.double_relative_ratio")));
                    }

                    //月追随用户数30天环比
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_follow.long_count")) {
                        oppoData.setMonthlyFollowedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_follow.long_count")));
                    }

                    //月追随用户数30天环比
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_follow.double_relative_ratio")) {
                        oppoData.setMonthlyFollowedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_follow.double_relative_ratio")));
                    }

                    //90_非商单播放中位数
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.long_readMedian")) {
                        oppoData.setNonCommercialPlayMedian90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.long_readMedian")));
                    }

                   //90_非商单完播率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_playOverRate")) {
                        oppoData.setNonCommercialCompletionRate90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_playOverRate")));
                    }

                    //90_非商单互动率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_interactionRate")) {
                        oppoData.setNonCommercialInteractionRate90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_interactionRate")));
                    }

                    //30_非商单播放中位数
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.long_readMedian")) {
                        oppoData.setNonCommercialPlayMedian30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.long_readMedian")));
                    }

                    //30_非商单完播率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_playOverRate")) {
                        oppoData.setNonCommercialCompletionRate30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_playOverRate")));
                    }

                    //30_非商单互动率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_interactionRate")) {
                        oppoData.setNonCommercialInteractionRate30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_interactionRate")));
                    }

                    //非商单发布作品数-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.long_postCount")) {
                        oppoData.setNonCommercialPostedWorksCount90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.long_postCount")));
                    }

                    //非商单平均时长-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_durationAvg")) {
                        oppoData.setNonCommercialAverageDuration90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_durationAvg")));
                    }

                    //非商单平均转发-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_shareAvg")) {
                        oppoData.setNonCommercialAverageForward90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_shareAvg")));
                    }

                    //非商单平均评论-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_commentAvg")) {
                        oppoData.setNonCommercialAverageComment90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_commentAvg")));
                    }

                    //非商单平均点赞-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dPerson.double_likeAvg")) {
                        oppoData.setNonCommercialAverageLike90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dPerson.double_likeAvg")));
                    }

                    //非商单发布作品数-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.long_postCount")) {
                        oppoData.setNonCommercialPostedWorksCount30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.long_postCount")));
                    }

                    //非商单平均时长-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_durationAvg")) {
                        oppoData.setNonCommercialAverageDuration30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_durationAvg")));
                    }

                    //非商单平均转发-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_shareAvg")) {
                        oppoData.setNonCommercialAverageForward30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_shareAvg")));
                    }

                    //非商单平均评论-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_commentAvg")) {
                        oppoData.setNonCommercialAverageComment30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_commentAvg")));
                    }

                    //非商单平均点赞-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dPerson.double_likeAvg")) {
                        oppoData.setNonCommercialAverageLike30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dPerson.double_likeAvg")));
                    }

                    //90_商单播放中位数
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.long_readMedian")) {
                        oppoData.setCommercialPlayMedian90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.long_readMedian")));
                    }

                    //90_商单完播率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_playOverRate")) {
                        oppoData.setCommercialCompletionRate90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_playOverRate")));
                    }

                    //90_商单互动率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_interactionRate")) {
                        oppoData.setCommercialInteractionRate90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_interactionRate")));
                    }

                    //30_商单播放中位数
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.long_readMedian")) {
                        oppoData.setCommercialPlayMedian30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.long_readMedian")));
                    }

                    //30_商单完播率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_playOverRate")) {
                        oppoData.setCommercialCompletionRate30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_playOverRate")));
                    }

                    //30_商单互动率
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_interactionRate")) {
                        oppoData.setCommercialInteractionRate30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_interactionRate")));
                    }

                    //商单发布作品数-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.long_postCount")) {
                        oppoData.setCommercialPostedWorksCount90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.long_postCount")));
                    }

                    //商单平均时长-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_durationAvg")) {
                        oppoData.setCommercialAverageDuration90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_durationAvg")));
                    }

                    //商单平均转发-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_shareAvg")) {
                        oppoData.setCommercialAverageForward90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_shareAvg")));
                    }

                    //商单平均评论-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_commentAvg")) {
                        oppoData.setCommercialAverageComment90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_commentAvg")));
                    }

                    //商单平均点赞-90天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n90dStar.double_likeAvg")) {
                        oppoData.setCommercialAverageLike90Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n90dStar.double_likeAvg")));
                    }

                    //商单发布作品数-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.long_postCount")) {
                        oppoData.setCommercialPostedWorksCount30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.long_postCount")));
                    }

                    //商单平均时长-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_durationAvg")) {
                        oppoData.setCommercialAverageDuration30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_durationAvg")));
                    }

                    //商单平均转发-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_shareAvg")) {
                        oppoData.setCommercialAverageForward30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_shareAvg")));
                    }

                    //商单平均评论-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_commentAvg")) {
                        oppoData.setCommercialAverageComment30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_commentAvg")));
                    }

                    //商单平均点赞-30天
                    if (sourceMap.containsKey("object_star.object_spreadInfo.object_n30dStar.double_likeAvg")) {
                        oppoData.setCommercialAverageLike30Days(String.valueOf(sourceMap.get("object_star.object_spreadInfo.object_n30dStar.double_likeAvg")));
                    }


                    // 内容二级标签_商单
                    if (sourceMap.containsKey("object_star.flattened_videoTouch.video_touch_sub")) {
                        oppoData.setContentSecondLevelTagForCommercial(String.valueOf(sourceMap.get("object_star.flattened_videoTouch.video_touch_sub")));
                    }

                    //douyinLink
                    if (sourceMap.containsKey("secuid")) {
                        oppoData.setDouyinLink("https://www.douyin.com/user/" + sourceMap.get("secuid"));
                    }


                    //预期CPM_20 - 60s视频
                    if (sourceMap.containsKey("object_star.double_expectedStarCpm20_60")) {
                        oppoData.setExpectedCpm20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpm20_60")));
                    }

                    //预期CPE_20 - 60s视频
                    if (sourceMap.containsKey("object_star.double_expectedStarCpe20_60")) {
                        oppoData.setExpectedCpe20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpe20_60")));
                    }
                    //达人身份标签
                    if (sourceMap.containsKey("object_star.kw_statusTag")) {
                        oppoData.setTalentStatusTag(String.valueOf(sourceMap.get("object_star.kw_statusTag")));
                    }


                    //达人社会身份标签
                    if (sourceMap.containsKey("object_star.kw_socialStatusTag")) {
                        oppoData.setTalentSocialStatusTag(String.valueOf(sourceMap.get("object_star.kw_socialStatusTag")));
                    }

                    //内容一级标签_商单
                    //内容二级标签_商单
                    //内容一级标签_非商单
                    //内容二级标签_非商单
                    parseVideoTouchTags(sourceMap,oppoData);


                    //月追随用户数30天环比
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_follow.double_relative_ratio")) {
                        oppoData.setMonthlyFollowedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_follow.double_relative_ratio")));
                    }

                    //15天增粉率
                    if (sourceMap.containsKey("object_star.long_n15dFansIncrement")) {
                        oppoData.setFansIncrementRate15Days(String.valueOf(sourceMap.get("object_star.long_n15dFansIncrement")));
                    }
                    //30天增粉率
                    if (sourceMap.containsKey("object_star.double_fansIncrementRate")) {
                        oppoData.setFansIncrementRate30Days(String.valueOf(sourceMap.get("object_star.double_fansIncrementRate")));
                    }
                    //近30天粉丝增长数
                    if (sourceMap.containsKey("object_star.long_n30dFansIncrement")) {
                        oppoData.setFansGrowthCountLast30Days(String.valueOf(sourceMap.get("object_star.long_n30dFansIncrement")));
                    }

                    //30_非商单互动量
                    long nonCommercialForward30Days = parseLongSafely(oppoData.getNonCommercialAverageForward30Days());
                    long nonCommercialComment30Days = parseLongSafely(oppoData.getNonCommercialAverageComment30Days());
                    long nonCommercialLike30Days = parseLongSafely(oppoData.getNonCommercialAverageLike30Days());
                    oppoData.setNonCommercialInteractionVolume30Days(String.valueOf(nonCommercialForward30Days + nonCommercialComment30Days + nonCommercialLike30Days));



                    //90_非商单互动量
                    long nonCommercialForward90Days = parseLongSafely(oppoData.getNonCommercialAverageForward90Days());
                    long nonCommercialComment90Days = parseLongSafely(oppoData.getNonCommercialAverageComment90Days());
                    long nonCommercialLike90Days = parseLongSafely(oppoData.getNonCommercialAverageLike90Days());
                    oppoData.setNonCommercialInteractionVolume90Days(String.valueOf(nonCommercialForward90Days + nonCommercialComment90Days + nonCommercialLike90Days));

                    //90_商单互动量
                    long commercialForward90Days = parseLongSafely(oppoData.getCommercialAverageForward90Days());
                    long commercialComment90Days = parseLongSafely(oppoData.getCommercialAverageComment90Days());
                    long commercialLike90Days = parseLongSafely(oppoData.getCommercialAverageLike90Days());
                    oppoData.setCommercialInteractionVolume90Days(String.valueOf(commercialForward90Days + commercialComment90Days + commercialLike90Days));


                    //30_非商单互动量
                    long commercialForward30Days = parseLongSafely(oppoData.getCommercialAverageForward30Days());
                    long commercialComment30Days = parseLongSafely(oppoData.getCommercialAverageComment30Days());
                    long commercialLike30Days = parseLongSafely(oppoData.getCommercialAverageLike30Days());
                    oppoData.setCommercialInteractionVolume30Days(String.valueOf(commercialForward30Days + commercialComment30Days + commercialLike30Days));



                    //30_商单互动量
                    long commercialInteractionVolume30Days = parseLongSafely(oppoData.getCommercialInteractionVolume30Days());
                    oppoData.setCommercialInteractionVolume30Days(String.valueOf(commercialInteractionVolume30Days));


                    //商业合作品牌
                    //3C - 商业合作品牌




                    //粉丝画像
                    Object fansDistribution = sourceMap.get("object_star.object_fansDistribution");
                    if(sourceMap.containsKey("object_star.object_fansDistribution") && fansDistribution != null){
                        // 安全地将对象转换为 JSON 字符串
                        String fansDistributionJson = toJsonStringSafely(fansDistribution);
                        oppoData.setObjectStarObjectFansDistribution(fansDistributionJson);
                    }

                    // 粉丝分布数据解析
                    if (fansDistribution != null) {
                        // 注意：这里不再重复设置原始数据，因为上面已经设置过了

                        try {
                            // 直接使用JSON.toJSONString处理对象，避免toString()带来的格式问题
                            String jsonStr = JSON.toJSONString(fansDistribution);
                            JSONArray fansDistributionArray = JSON.parseArray(jsonStr);

                            if (fansDistributionArray != null && !fansDistributionArray.isEmpty()) {
                                for (int i = 0; i < fansDistributionArray.size(); i++) {
                                    JSONObject dict = fansDistributionArray.getJSONObject(i);
                                    if (dict != null) {
                                        String kwDisplay = dict.getString("kw_display");

                                        // 使用常量或枚举替代硬编码字符串
                                        if ("省份分布".equals(kwDisplay)) {
                                            // 获取并设置地域分布数据
                                            JSONArray regionDistributionArray = dict.getJSONArray("object_distribution");
                                            if (regionDistributionArray != null) {
                                                oppoData.setFansRegionDistribution(regionDistributionArray.toJSONString());
                                            }
                                        }
                                        //粉丝设备分布
                                        if ("设备品牌分布".equals(kwDisplay)) {
                                            // 获取并设置设备分布数据
                                            JSONArray deviceDistributionArray = dict.getJSONArray("object_distribution");
                                            if (deviceDistributionArray != null) {
                                                oppoData.setFansDeviceDistribution(deviceDistributionArray.toJSONString());
                                            }
                                        }
                                        //粉丝城市分布
                                        if ("城市分布".equals(kwDisplay)) {
                                            // 获取并设置城市分布数据
                                            JSONArray cityDistributionArray = dict.getJSONArray("object_distribution");
                                            if (cityDistributionArray != null) {
                                                oppoData.setFansCityDistribution(cityDistributionArray.toJSONString());
                                            }
                                        }
                                        //粉丝性别分布
                                        if ("性别分布".equals(kwDisplay)) {
                                            // 获取并设置性别分布数据
                                            JSONArray genderDistributionArray = dict.getJSONArray("object_distribution");
                                            if (genderDistributionArray != null) {
                                                oppoData.setFansGenderDistribution(genderDistributionArray.toJSONString());
                                            }
                                        }
                                        //粉丝年龄分布
                                        if ("年龄分布".equals(kwDisplay)) {
                                            // 获取并设置年龄分布数据
                                            JSONArray ageDistributionArray = dict.getJSONArray("object_distribution");
                                            if (ageDistributionArray != null) {
                                                oppoData.setFansAgeDistribution(ageDistributionArray.toJSONString());
                                            }
                                        }

                                        //粉丝城市等级
                                        if ("城市等级分布".equals(kwDisplay)) {
                                            // 获取并设置城市等级数据
                                            JSONArray cityLevelArray = dict.getJSONArray("object_distribution");
                                            if (cityLevelArray != null) {
                                                oppoData.setFansCityLevel(cityLevelArray.toJSONString());
                                            }
                                        }
                                        //粉丝八大人群
                                        if ("八大人群分布".equals(kwDisplay)) {
                                            // 获取并设置八大人群数据
                                            JSONArray eightGroupsArray = dict.getJSONArray("object_distribution");
                                            if (eightGroupsArray != null) {
                                                oppoData.setFansEightGroups(eightGroupsArray.toJSONString());
                                            }
                                        }


                                    }
                                }
                            }
                        } catch (JSONException e) {
                            // 记录详细的异常信息，便于排查问题
                            logger.error("解析粉丝分布数据失败: {}", e.getMessage(), e);

                        }
                    }

                    //观众画像
                    if(sourceMap.containsKey("object_star.object_viewersDistribution")){
                        Object viewersObject = sourceMap.get("object_star.object_viewersDistribution");

                        // 安全地将对象转换为 JSON 字符串
                        String viewersDistributionJson = toJsonStringSafely(viewersObject);
                        oppoData.setObjectStarObjectViewersDistribution(viewersDistributionJson);
                        String jsonStr = JSON.toJSONString(viewersObject);
                        JSONArray viewerArray = JSON.parseArray(jsonStr);
                        if (viewerArray != null && !viewerArray.isEmpty()) {
                            for (int i = 0; i < viewerArray.size(); i++) {
                                JSONObject dict = viewerArray.getJSONObject(i);
                                if (dict != null) {
                                    String kwDisplay = dict.getString("kw_display");

                                    // 使用常量或枚举替代硬编码字符串
                                    if ("性别分布".equals(kwDisplay)) {
                                        // 获取并设置性别分布数据
                                        JSONArray genderDistributionArray = dict.getJSONArray("object_distribution");
                                        if (genderDistributionArray != null) {
                                            oppoData.setViewersGenderDistribution(genderDistributionArray.toJSONString());
                                        }
                                    }
                                    //观众年龄分布
                                    if ("年龄分布".equals(kwDisplay)) {
                                        // 获取并设置年龄分布数据
                                        JSONArray ageDistributionArray = dict.getJSONArray("object_distribution");
                                        if (ageDistributionArray != null) {
                                            oppoData.setViewersAgeDistribution(ageDistributionArray.toJSONString());
                                        }
                                    }
                                    //观众地域分布
                                    if ("省份分布".equals(kwDisplay)) {
                                        // 获取并设置地域分布数据
                                        JSONArray regionDistributionArray = dict.getJSONArray("object_distribution");
                                        if (regionDistributionArray != null) {
                                            oppoData.setViewersRegionDistribution(regionDistributionArray.toJSONString());
                                        }
                                    }
                                    //观众城市分布
                                    if ("城市分布".equals(kwDisplay)) {
                                        // 获取并设置城市分布数据
                                        JSONArray cityDistributionArray = dict.getJSONArray("object_distribution");
                                        if (cityDistributionArray != null) {
                                            oppoData.setViewersCityDistribution(cityDistributionArray.toJSONString());
                                        }
                                    }
                                    //观众城市等级分布
                                    if ("城市等级分布".equals(kwDisplay)) {
                                        // 获取并设置城市等级数据
                                        JSONArray cityLevelArray = dict.getJSONArray("object_distribution");
                                        if (cityLevelArray != null) {
                                            oppoData.setViewersCityLevelDistribution(cityLevelArray.toJSONString());
                                        }
                                    }

                                    if ("设备品牌分布".equals(kwDisplay)) {
                                        // 获取并设置设备分布数据
                                        JSONArray deviceDistributionArray = dict.getJSONArray("object_distribution");
                                        if (deviceDistributionArray != null) {
                                            oppoData.setViewersDeviceDistribution(deviceDistributionArray.toJSONString());
                                        }
                                    }

                                    if ("八大人群分布".equals(kwDisplay)) {
                                        // 获取并设置八大人群数据
                                        JSONArray eightGroupsArray = dict.getJSONArray("object_distribution");
                                        if (eightGroupsArray != null) {
                                            oppoData.setViewersEightGroupsDistribution(eightGroupsArray.toJSONString());
                                        }
                                    }

                                }
                            }
                        }
                    }

                    // 设置时间字段
                    LocalDateTime now = LocalDateTime.now();
                    oppoData.setCreateTime(now);
                    oppoData.setUpdatedTime(now);

                    // 添加到结果列表
                    oppoDataList.add(oppoData);
                });
            }
        }

        return oppoDataList;
    }


    public void parseVideoTouchTags(Map<String, Object> sourceMap, OppoData oppoData) {
        // 获取video_touch数据
        Object videoTouchObj = sourceMap.get("object_star.flattened_videoTouch.video_touch");

        if (videoTouchObj instanceof List) {
            List<Map<String, Object>> videoTouchList = (List<Map<String, Object>>) videoTouchObj;

            // 初始化商单和非商单标签
            String commercialFirstLevelTag = "";
            String commercialSecondLevelTag = "";
            String nonCommercialFirstLevelTag = "";
            String nonCommercialSecondLevelTag = "";

            // 遍历video_touch列表
            for (Map<String, Object> firstLevelItem : videoTouchList) {
                Map<String, Object> distributionData = (Map<String, Object>) firstLevelItem.get("distribution_data");
                String firstLevelName = (String) distributionData.get("name");
                Integer firstLevelCount = ((Number) distributionData.get("cnt")).intValue();

                // 判断是否为商单（根据分析，"星图"属于商单）
                if ("星图".equals(firstLevelName)) {
                    commercialFirstLevelTag = firstLevelName;

                    // 处理二级分类
                    List<Map<String, Object>> secondDistribution = (List<Map<String, Object>>) firstLevelItem.get("second_distribution");
                    if (secondDistribution != null && !secondDistribution.isEmpty()) {
                        Map<String, Object> secondLevelItem = secondDistribution.get(0);
                        Map<String, Object> secondLevelData = (Map<String, Object>) secondLevelItem.get("distribution_data");
                        commercialSecondLevelTag = (String) secondLevelData.get("name");
                    }
                }
                // 其他为非商单（根据分析，"个人"属于非商单）
                else if ("个人".equals(firstLevelName)) {
                    nonCommercialFirstLevelTag = firstLevelName;

                    // 找出非商单中数量最大的二级分类作为主要标签
                    List<Map<String, Object>> secondDistribution = (List<Map<String, Object>>) firstLevelItem.get("second_distribution");
                    String maxSecondLevelName = "";
                    int maxSecondLevelCount = 0;

                    if (secondDistribution != null) {
                        for (Map<String, Object> secondLevelItem : secondDistribution) {
                            Map<String, Object> secondLevelData = (Map<String, Object>) secondLevelItem.get("distribution_data");
                            String secondLevelName = (String) secondLevelData.get("name");
                            Integer secondLevelCount = ((Number) secondLevelData.get("cnt")).intValue();

                            if (secondLevelCount > maxSecondLevelCount) {
                                maxSecondLevelCount = secondLevelCount;
                                maxSecondLevelName = secondLevelName;
                            }
                        }
                    }

                    nonCommercialSecondLevelTag = maxSecondLevelName;
                }
            }

            // 设置解析结果到OppoData对象
            oppoData.setContentFirstLevelTagForCommercial(commercialFirstLevelTag);
            oppoData.setContentSecondLevelTagForCommercial(commercialSecondLevelTag);
            oppoData.setContentFirstLevelTagForNonCommercial(nonCommercialFirstLevelTag);
            oppoData.setContentSecondLevelTagForNonCommercial(nonCommercialSecondLevelTag);
        }
    }


    public void saveAll(List<OppoData> oppoDataList) {
        try {
            if (oppoDataList == null || oppoDataList.isEmpty()) {
                logger.warn("数据列表为空，无需保存");
                return;
            }

            // 获取当前周的表名
            String currentWeekTable = WeeklyTableUtil.getCurrentWeekTableName();
            logger.info("开始批量保存数据到表 {}，共 {} 条记录", currentWeekTable, oppoDataList.size());

            // 确保当前周表存在
            ensureWeeklyTableExists(currentWeekTable);

            // 分批保存，避免SQL语句过长
            int batchSize = 100;
            for (int i = 0; i < oppoDataList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, oppoDataList.size());
                List<OppoData> batch = oppoDataList.subList(i, endIndex);

                logger.debug("保存第 {} 批数据到表 {}，共 {} 条", (i / batchSize) + 1, currentWeekTable, batch.size());
                int insertCount = oppoDataMapper.batchInsertToTable(currentWeekTable, batch);
                logger.debug("第 {} 批数据保存完成，实际插入 {} 条", (i / batchSize) + 1, insertCount);
            }

            logger.info("批量保存数据完成，共处理 {} 条记录到表 {}", oppoDataList.size(), currentWeekTable);

        } catch (Exception e) {
            logger.error("批量保存数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确保周表存在，如果不存在则创建
     * @param tableName 表名
     */
    private void ensureWeeklyTableExists(String tableName) {
        try {
            if (oppoDataMapper.checkTableExists(tableName) == 0) {
                logger.info("表 {} 不存在，开始创建", tableName);

                // 使用第一周的表作为模板
                String templateTable = "oppo_data_2025_01";
                oppoDataMapper.createWeeklyTable(tableName, templateTable);

                logger.info("表 {} 创建成功", tableName);
            } else {
                logger.debug("表 {} 已存在", tableName);
            }
        } catch (Exception e) {
            logger.error("创建表 {} 失败: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("创建周表失败: " + e.getMessage(), e);
        }
    }

    public List<OppoData> getDataByUserId(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                logger.warn("用户ID为空，无法查询");
                return new ArrayList<>();
            }

            logger.debug("开始从周表查询用户ID: {}", userId);

            // 查询最近4周的数据
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(4);
            List<OppoData> allResults = new ArrayList<>();

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        List<OppoData> tableResults = oppoDataMapper.selectByUserIdFromTable(tableName, userId);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("周表查询完成，用户ID: {} 总共返回 {} 条记录", userId, allResults.size());
            return allResults;

        } catch (Exception e) {
            logger.error("根据用户ID查询数据失败: userId={}, error={}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public OppoData getDataByStarMapId(String starMapId) {
        try {
            if (!StringUtils.hasText(starMapId)) {
                logger.warn("星图ID为空，无法查询");
                return null;
            }

            logger.debug("开始从数据库查询星图ID: {}", starMapId);
            OppoData result = oppoDataMapper.selectByStarMapId(starMapId);

            logger.debug("数据库查询完成，星图ID: {} 返回结果: {}", starMapId, result != null ? "找到数据" : "未找到数据");
            return result;

        } catch (Exception e) {
            logger.error("根据星图ID查询数据失败: starMapId={}, error={}", starMapId, e.getMessage(), e);
            return null;
        }
    }

    public List<OppoData> getAllData() {
        try {
            logger.debug("开始从数据库查询所有数据");
            List<OppoData> result = oppoDataMapper.selectList(null);

            if (result == null) {
                result = new ArrayList<>();
            }

            logger.debug("数据库查询完成，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            logger.error("查询所有数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 分页查询OppoData数据（先查数据库，没有则调用API）
     * @param request 分页查询请求
     * @return 分页响应结果
     */
    public PageResponse<OppoData> queryOppoDataByPage(PageQueryRequest request) {
        try {
            // 校验Token
            tokenValidationService.validateTokenOrThrow(request.getToken());

            logger.info("开始分页查询OppoData，页码: {}, 页大小: {}", request.getPageNum(), request.getPageSize());

            // 先从数据库查询
            PageResponse<OppoData> dbResult = queryOppoDataFromDatabase(request);

            // 如果数据库中有数据，直接返回
            if (dbResult.getTotal() > 0) {
                logger.info("从数据库查询到数据，返回 {} 条记录，总计 {} 条",
                           dbResult.getData().size(), dbResult.getTotal());
                return dbResult;
            }

            //logger.info("数据库中没有数据，调用API接口查询");
            List<OppoData> oppoDataList = new ArrayList<>();
            // 数据库中没有数据，调用API接口
            //DocSourceRequest docSourceRequest = buildDocSourceRequest(request);
           // Object result = queryDocSource(docSourceRequest);
           // oppoDataList = convertToOppoDataList(result);

            // 如果API返回了数据，保存到数据库
           /* if (!oppoDataList.isEmpty()) {
                logger.info("API查询到 {} 条数据，保存到数据库", oppoDataList.size());
                saveAll(oppoDataList);
            }*/

            // 计算分页信息
            int pageNum = request.getPageNum();
            int pageSize = request.getPageSize();
            long total = oppoDataList.size();

            // 计算分页数据
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, oppoDataList.size());

            List<OppoData> pageData = new ArrayList<>();
            if (startIndex < oppoDataList.size()) {
                pageData = oppoDataList.subList(startIndex, endIndex);
            }

            logger.info("API分页查询完成，返回 {} 条记录", pageData.size());

            return PageResponse.of(pageNum, pageSize, total, pageData);

        } catch (Exception e) {
            logger.error("分页查询OppoData失败: {}", e.getMessage(), e);
            throw new RuntimeException("分页查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从数据库分页查询OppoData数据
     * @param request 分页查询请求
     * @return 分页响应结果
     */
    private PageResponse<OppoData> queryOppoDataFromDatabase(PageQueryRequest request) {
        try {
            // 计算分页参数
            int pageNum = request.getPageNum();
            int pageSize = request.getPageSize();
            int offset = (pageNum - 1) * pageSize;

            List<OppoData> allDataList = new ArrayList<>();

            if (StringUtils.hasText(request.getUserIds())) {
                // 有用户ID条件：批量查询所有表中符合userIds的数据
                logger.debug("根据用户ID批量查询数据: {}", request.getUserIds());
                allDataList = queryDataByUserIds(request.getUserIds());
            } else {
                // 无用户ID条件：查询最近两周的所有数据
                logger.debug("查询最近两周的所有数据");
                allDataList = queryRecentTwoWeeksAllData();
            }

            // 计算总数
            long total = allDataList.size();

            // 手动分页
            List<OppoData> pageDataList = new ArrayList<>();
            if (total > 0) {
                int startIndex = Math.min(offset, allDataList.size());
                int endIndex = Math.min(offset + pageSize, allDataList.size());
                if (startIndex < allDataList.size()) {
                    pageDataList = allDataList.subList(startIndex, endIndex);
                }
            }

            logger.debug("数据库查询结果: 总计 {} 条，当前页 {} 条", total, pageDataList.size());

            return PageResponse.of(pageNum, pageSize, total, pageDataList);

        } catch (Exception e) {
            logger.error("数据库分页查询失败: {}", e.getMessage(), e);
            // 返回空结果，让程序继续调用API
            return PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
        }
    }

    /**
     * 根据用户ID列表批量查询数据（查询所有表）
     * @param userIds 用户ID列表（逗号分隔）
     * @return OppoData列表
     */
    private List<OppoData> queryDataByUserIds(String userIds) {
        List<OppoData> allResults = new ArrayList<>();

        try {
            // 解析用户ID列表
            String[] userIdArray = userIds.split(",");
            List<String> userIdList = new ArrayList<>();
            for (String userId : userIdArray) {
                String trimmedUserId = userId.trim();
                if (StringUtils.hasText(trimmedUserId)) {
                    userIdList.add(trimmedUserId);
                }
            }

            if (userIdList.isEmpty()) {
                logger.warn("用户ID列表为空");
                return allResults;
            }

            // 获取最近4周的表名（扩大查询范围以确保数据完整性）
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(4);

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        // 批量查询该表中所有符合条件的用户数据
                        List<OppoData> tableResults = queryUserDataFromTable(tableName, userIdList);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("批量用户ID查询完成，总共返回 {} 条记录", allResults.size());

        } catch (Exception e) {
            logger.error("批量查询用户数据失败: userIds={}, error={}", userIds, e.getMessage(), e);
        }

        return allResults;
    }

    /**
     * 查询最近两周的所有数据
     * @return OppoData列表
     */
    private List<OppoData> queryRecentTwoWeeksAllData() {
        List<OppoData> allResults = new ArrayList<>();

        try {
            // 获取最近两周的表名
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(2);

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        // 查询该表的所有数据
                        List<OppoData> tableResults = oppoDataMapper.selectAllFromTable(tableName);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("最近两周数据查询完成，总共返回 {} 条记录", allResults.size());

        } catch (Exception e) {
            logger.error("查询最近两周数据失败: {}", e.getMessage(), e);
        }

        return allResults;
    }

    /**
     * 根据用户ID列表分页查询指定周表数据
     * @param userIds 用户ID列表
     * @param tableName 指定的周表名（如：oppo_data_2025_27）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页响应结果
     */
    public PageResponse<OppoData> queryByUserIdsFromWeekTable(List<String> userIds, String tableName, int pageNum, int pageSize) {
        try {
            logger.info("开始从周表 {} 查询用户数据，用户ID数量: {}, 页码: {}, 页大小: {}",
                       tableName, userIds.size(), pageNum, pageSize);

            if (userIds == null || userIds.isEmpty()) {
                logger.warn("用户ID列表为空");
                return PageResponse.of(pageNum, pageSize, 0L, new ArrayList<>());
            }

            // 检查表是否存在
            if (oppoDataMapper.checkTableExists(tableName) == 0) {
                logger.warn("表 {} 不存在", tableName);
                return PageResponse.of(pageNum, pageSize, 0L, new ArrayList<>());
            }

            // 先查询总数
            int totalCount = oppoDataMapper.countByUserIdsFromTable(tableName, userIds);

            if (totalCount == 0) {
                logger.info("表 {} 中没有找到匹配的用户数据", tableName);
                return PageResponse.of(pageNum, pageSize, 0L, new ArrayList<>());
            }

            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;

            // 分页查询数据
            List<OppoData> dataList = oppoDataMapper.selectByUserIdsFromTableWithPage(tableName, userIds, offset, pageSize);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            logger.info("从表 {} 查询完成，总计: {} 条，当前页: {} 条", tableName, totalCount, dataList.size());

            PageResponse<OppoData> response = new PageResponse<>();
            response.setPageNum(pageNum);
            response.setPageSize(pageSize);
            response.setTotal((long) totalCount);
            response.setTotalPages(totalPages);
            response.setData(dataList != null ? dataList : new ArrayList<>());

            return response;

        } catch (Exception e) {
            logger.error("从周表查询用户数据失败: tableName={}, userIds={}, error={}",
                        tableName, userIds, e.getMessage(), e);
            return PageResponse.of(pageNum, pageSize, 0L, new ArrayList<>());
        }
    }

    /**
     * 从指定表中查询用户数据
     * @param tableName 表名
     * @param userIds 用户ID列表
     * @return OppoData列表
     */
    private List<OppoData> queryUserDataFromTable(String tableName, List<String> userIds) {
        try {
            if (userIds == null || userIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 使用批量查询方法
            List<OppoData> results = oppoDataMapper.selectByUserIdsFromTable(tableName, userIds);
            return results != null ? results : new ArrayList<>();

        } catch (Exception e) {
            logger.error("从表 {} 查询用户数据失败: userIds={}, error={}", tableName, userIds, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建DocSourceRequest
     * @param request 分页查询请求
     * @return DocSourceRequest
     */
    private DocSourceRequest buildDocSourceRequest(PageQueryRequest request) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();

        // 设置extraCondition - 确保字段顺序与正常参数一致
        List<Object> extraCondition = new ArrayList<>();
        Map<String, Object> condition = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
        condition.put("fieldId", 446);
        condition.put("operator", 0);
        String userIds = request.getUserIds();
        // 直接使用逗号分隔的字符串，不添加引号
        List<String> userIdList = Arrays.stream(userIds.split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        condition.put("value", userIdList);
        extraCondition.add(condition);
        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(341497));

        // 设置其他必要参数 - 确保与正常参数一致
        docSourceRequest.setIndexType("user");
        docSourceRequest.setPath("oppo");
        docSourceRequest.setPageNum(request.getPageNum() != null ? request.getPageNum() : 1);
        docSourceRequest.setPageSize(request.getPageSize() != null ? request.getPageSize() : 100);
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10); // 使用10而不是100
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>()); // 使用空列表而不是null
        docSourceRequest.setMetricsVo(new ArrayList<>()); // 使用空列表而不是null
        docSourceRequest.setToken(request.getToken());

        // 设置downloadCount
        docSourceRequest.setDownloadCount(Collections.singletonList("0"));
        return docSourceRequest;
    }

    public List<OppoData> createQueryQuarkData(List<String> userIds) {
        // 每批处理的用户数量
        int batchSize = 100;
        // 最大重试次数
        int maxRetries = 3;
        List<OppoData> allOppoDataList = new ArrayList<>();
        // 记录失败的批次
        List<Integer> failedBatches = new ArrayList<>();

        logger.info("开始处理用户数据查询，总用户数: {}, 批次大小: {}", userIds.size(), batchSize);

        // 计算批次数量
        int totalBatches = (int) Math.ceil((double) userIds.size() / batchSize);
        logger.info("总批次数: {}", totalBatches);

        for (int pageNum = 1; pageNum <= totalBatches; pageNum++) {
            boolean batchSuccess = false;
            int retryCount = 0;

            while (!batchSuccess && retryCount < maxRetries) {
                try {
                    // 计算当前批次的用户ID范围
                    int fromIndex = (pageNum - 1) * batchSize;
                    int toIndex = Math.min(fromIndex + batchSize, userIds.size());
                    List<String> currentUserIds = userIds.subList(fromIndex, toIndex);

                    logger.info("处理第 {}/{} 批次，用户ID数量: {} (索引 {} 到 {})",
                               pageNum, totalBatches, currentUserIds.size(), fromIndex, toIndex - 1);

                    // 查询当前批次的所有数据（处理多页结果）
                    List<OppoData> batchOppoDataList = queryAllDataForUserIds(currentUserIds, pageNum);

                    // 检查转换结果
                    if (batchOppoDataList == null) {
                        batchOppoDataList = Collections.emptyList();
                    }

                    allOppoDataList.addAll(batchOppoDataList);

                    // 保存数据，使用单独的事务
                    saveDataInNewTransaction(batchOppoDataList);

                    batchSuccess = true;
                    logger.info("批次 {}/{} 处理成功，获取数据: {} 条", pageNum, totalBatches, batchOppoDataList.size());
                } catch (Exception e) {
                    retryCount++;
                    logger.error("处理批次数据失败，批次号: {}, 重试次数: {}/{}, 异常信息: {}",
                            pageNum, retryCount, maxRetries, e.getMessage(), e);

                    // 重试前等待一段时间
                    if (!batchSuccess && retryCount < maxRetries) {
                        try {
                            Thread.sleep(1000 * retryCount); // 指数退避策略
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            }

            if (!batchSuccess) {
                failedBatches.add(pageNum);
                logger.error("批次处理完全失败，批次号: {}, 用户ID范围: {} - {}",
                        pageNum, (pageNum-1)*batchSize, Math.min(pageNum*batchSize, userIds.size()));
            }
        }

        // 处理完全失败的批次
        if (!failedBatches.isEmpty()) {
            logger.error("有 {} 个批次处理失败，批次号: {}", failedBatches.size(), failedBatches);
            // 可以在这里添加额外的处理逻辑，如发送警报
        }

        logger.info("所有批次处理完成，总获取数据: {} 条", allOppoDataList.size());
        return allOppoDataList;
    }

    // 创建请求对象的辅助方法，提高代码可读性
    private DocSourceRequest createDocSourceRequest(List<String> userIds, int pageNum, int pageSize) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();

        // 设置extraCondition
        List<Object> extraCondition = new ArrayList<>();
        Map<String, Object> condition = new LinkedHashMap<>();
        condition.put("fieldId", 446);
        condition.put("operator", 0);
        condition.put("value", userIds);
        extraCondition.add(condition);
        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(341497));

        // 设置分页参数
        docSourceRequest.setPageNum(pageNum);
        docSourceRequest.setPageSize(pageSize);

        // 设置其他必要参数
        docSourceRequest.setIndexType("user");
        docSourceRequest.setPath("oppo");
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10);
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>());
        docSourceRequest.setMetricsVo(new ArrayList<>());
        docSourceRequest.setDownloadCount(Collections.singletonList("0"));

        return docSourceRequest;
    }

    /**
     * 查询指定用户ID列表的所有数据（处理多页结果）
     * @param userIds 用户ID列表
     * @param batchNum 批次号（用于日志）
     * @return 所有数据列表
     */
    private List<OppoData> queryAllDataForUserIds(List<String> userIds, int batchNum) {
        List<OppoData> allData = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 100; // 每页大小
        boolean hasMoreData = true;

        logger.info("开始查询批次 {} 的数据，用户ID数量: {}", batchNum, userIds.size());

        while (hasMoreData) {
            try {
                // 创建DocSourceRequest对象，注意这里pageNum是真正的页码
                DocSourceRequest docSourceRequest = createDocSourceRequestForPage(userIds, pageNum, pageSize);

                // 调用服务查询user
                Object result = queryDocSource(docSourceRequest);

                // 转换结果为List<OppoData>
                List<OppoData> pageData = convertToOppoDataList(result);

                //调用服务查询post 获取商业合作品牌的数据
                DocSourceRequest postDocSourceRequestForPage = createPostDocSourceRequestForPage(userIds, pageNum, pageSize);
                Object postObject = queryDocSource(postDocSourceRequestForPage);
                convertToOppoDataListFromPost(postObject,pageData);


                if (pageData == null || pageData.isEmpty()) {
                    // 没有更多数据了
                    hasMoreData = false;
                    logger.info("批次 {} 第 {} 页没有数据，查询结束", batchNum, pageNum);
                } else {
                    allData.addAll(pageData);
                    logger.info("批次 {} 第 {} 页获取到 {} 条数据", batchNum, pageNum, pageData.size());

                    // 如果返回的数据少于pageSize，说明是最后一页
                    if (pageData.size() < pageSize) {
                        hasMoreData = false;
                        logger.info("批次 {} 第 {} 页数据不足 {} 条，判断为最后一页", batchNum, pageNum, pageSize);
                    } else {
                        pageNum++; // 继续查询下一页
                    }
                }

            } catch (Exception e) {
                logger.error("查询批次 {} 第 {} 页数据失败: {}", batchNum, pageNum, e.getMessage(), e);
                hasMoreData = false; // 出错时停止查询
            }
        }

        logger.info("批次 {} 查询完成，总共获取 {} 条数据", batchNum, allData.size());
        return allData;
    }

    private void convertToOppoDataListFromPost(Object postObject, List<OppoData> pageData) {
        if (!(postObject instanceof Map)) {
            logger.error("postObject不是Map类型");
            return;
        }

        Map<String, Object> resultMap = (Map<String, Object>) postObject;
        Object dataObj = resultMap.get("data");

        if (dataObj == null) {
            logger.error("缺少data字段或data字段为空");
            return;
        }

        List<Map<String, Object>> dataList = new ArrayList<>();

        if (dataObj instanceof List) {
            List<?> rawList = (List<?>) dataObj;
            if (rawList.stream().allMatch(item -> item instanceof Map)) {
                dataList = rawList.stream()
                        .map(item -> (Map<String, Object>) item)
                        .collect(Collectors.toList());
            } else {
                logger.error("列表中存在非Map类型的数据");
                return;
            }
        } else if (dataObj instanceof Map) {
            dataList.add((Map<String, Object>) dataObj);
        } else {
            logger.error("data字段类型错误，期望List或Map");
            return;
        }

        if (dataList.isEmpty()) {
            logger.info("没有查询到数据");
            return;
        }

        // 提取所有记录中的rows数据
        List<Map<String, Object>> allRows = new ArrayList<>();
        for (Map<String, Object> dataMap : dataList) {
            Object rowsObj = dataMap.get("rows");
            if (rowsObj instanceof List) {
                List<?> rowsList = (List<?>) rowsObj;
                for (Object rowObj : rowsList) {
                    if (rowObj instanceof Map) {
                        allRows.add((Map<String, Object>) rowObj);
                    }
                }
            }
        }

        // 处理每条记录
        for (Map<String, Object> row : allRows) {
            Map<String, Object> sourceMap = getSourceMap(row);
            if (sourceMap == null) {
                continue;
            }

            // 获取userId，这是必须的字段
            String userId = (String) sourceMap.get("object_user.kw_userId");
            if (userId == null) {
                logger.warn("userId为空，跳过此条记录");
                continue;
            }

            // 处理 kw_brand 字段
            Object kwBrandObj = sourceMap.get("kw_brand");
            String kwBrand = "";
            String threeCCommercialCooperationBrands = "";

            if (kwBrandObj != null) {
                if (kwBrandObj instanceof String) {
                    // 如果是字符串类型
                    kwBrand = (String) kwBrandObj;
                } else if (kwBrandObj instanceof java.util.List) {
                    // 如果是列表类型，将列表元素用逗号连接
                    @SuppressWarnings("unchecked")
                    java.util.List<Object> kwBrandList = (java.util.List<Object>) kwBrandObj;
                    if (!kwBrandList.isEmpty()) {
                        kwBrand = kwBrandList.stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(","));
                    }
                } else {
                    // 其他类型，转换为字符串
                    kwBrand = kwBrandObj.toString();
                }

                if (kwBrand != null && !kwBrand.trim().isEmpty()) {
                    logger.debug("处理kwBrand: {} (原始类型: {})", kwBrand, kwBrandObj.getClass().getSimpleName());

                    //商业合作品牌中包含vivo、小米、xiaomi、OPPO、华为、huawei、三星、Samsung 、红米、redmi、一加、oneplus、荣耀、HONOR、苹果、iPhone、Apple等品牌则记为3C合作品牌
                    threeCCommercialCooperationBrands = extractThreeCBrands(kwBrand);
                } else {
                    logger.debug("kwBrand处理后为空，但继续处理其他品牌信息，原始类型: {}", kwBrandObj.getClass().getSimpleName());
                }
            } else {
                logger.debug("kwBrand为空，但继续处理其他品牌信息");
            }

            String businessCooperationBrand = buildBusinessCooperationBrand(sourceMap);

            // 检查是否有任何品牌信息需要更新
            boolean hasBusinessBrand = !businessCooperationBrand.isEmpty();
            boolean hasThreeCBrand = !threeCCommercialCooperationBrands.isEmpty();

            if (!hasBusinessBrand && !hasThreeCBrand) {
                logger.debug("用户{}既没有商业合作品牌也没有3C合作品牌，跳过更新", userId);
                continue;
            }

            logger.debug("用户{}品牌信息: 商业合作品牌={}, 3C合作品牌={}",
                        userId,
                        hasBusinessBrand ? businessCooperationBrand : "无",
                        hasThreeCBrand ? threeCCommercialCooperationBrands : "无");

            // 根据userId查询并更新数据（即使其中一个品牌为空也要更新）
            updateOppoData(userId, businessCooperationBrand, threeCCommercialCooperationBrands, pageData);
        }
    }

    // 提取sourceMap的单独方法
    private Map<String, Object> getSourceMap(Map<String, Object> row) {
        Object sourceObj = row.get("source");
        if (sourceObj instanceof Map) {
            return (Map<String, Object>) sourceObj;
        }
        logger.warn("source不是Map类型，跳过此条记录");
        return null;
    }

    /**
     * 提取3C品牌
     * @param kwBrand 商业合作品牌字符串
     * @return 匹配的3C品牌，用逗号分隔
     */
    private String extractThreeCBrands(String kwBrand) {
        if (kwBrand == null || kwBrand.trim().isEmpty()) {
            return "";
        }

        // 定义3C品牌关键词（不区分大小写）
        String[] threeCBrandKeywords = {
            "vivo", "小米", "xiaomi", "OPPO", "华为", "huawei",
            "三星", "Samsung", "红米", "redmi", "一加", "oneplus",
            "荣耀", "HONOR", "苹果", "iPhone", "Apple"
        };

        // 使用LinkedHashSet保持顺序并去重
        Set<String> matchedBrands = new LinkedHashSet<>();

        // 将kwBrand转换为小写进行匹配
        String kwBrandLower = kwBrand.toLowerCase();

        // 遍历所有3C品牌关键词
        for (String keyword : threeCBrandKeywords) {
            String keywordLower = keyword.toLowerCase();

            // 检查是否包含该关键词
            if (kwBrandLower.contains(keywordLower)) {
                // 添加原始关键词（保持原有大小写）
                matchedBrands.add(keyword);

                // 记录匹配日志
                logger.debug("在kwBrand中找到3C品牌: {} (原文: {})", keyword, kwBrand);
            }
        }

        // 将匹配的品牌用逗号连接
        String result = String.join(",", matchedBrands);

        if (!result.isEmpty()) {
            logger.info("提取到3C合作品牌: {} (来源: {})", result, kwBrand);
        }

        return result;
    }

    private String buildBusinessCooperationBrand(Map<String, Object> sourceMap) {
        // 使用HashSet存储品牌，自动去重
        Set<String> brandSet = new HashSet<>();
        appendBrandIfExists(sourceMap, "kw_prestigeBrand", brandSet);
        appendBrandIfExists(sourceMap, "kw_skinBrand", brandSet);
        appendBrandIfExists(sourceMap, "kw_pccBrand", brandSet);
        appendBrandIfExists(sourceMap, "kw_hairBrand", brandSet);
        appendBrandIfExists(sourceMap, "kw_babyBrand", brandSet);
        appendBrandIfExists(sourceMap, "kw_groomingBrand", brandSet);

        // 将Set转换为字符串，保持[品牌]格式
        StringBuilder brandBuilder = new StringBuilder();
        for (String brand : brandSet) {
            brandBuilder.append("[").append(brand).append("]");
        }
        return brandBuilder.toString().trim();
    }

    /**
     * 重载appendBrandIfExists方法，适配Set参数
     */
    private void appendBrandIfExists(Map<String, Object> sourceMap, String key, Set<String> brandSet) {
        if (sourceMap.containsKey(key)) {
            Object value = sourceMap.get(key);
            if (value instanceof String) {
                String brand = (String) value;
                if (!StringUtils.isEmpty(brand.trim())) {
                    brandSet.add(brand.trim()); // 添加前去除空格
                }
            } else if (value instanceof Collection) {
                // 处理品牌列表情况
                for (Object item : (Collection<?>) value) {
                    if (item instanceof String) {
                        String brand = ((String) item).trim();
                        if (!StringUtils.isEmpty(brand.trim())) {
                            brandSet.add(brand);
                        }
                    }
                }
            }
        }
    }

    // 根据userId更新OppoData的方法 商业合作品牌和3C合作品牌
    private void updateOppoData(String userId, String businessCooperationBrand, String threeCCommercialCooperationBrands, List<OppoData> pageData) {
        // 查找是否已有该userId的数据
        for (OppoData oppoData : pageData) {
            if (userId.equals(oppoData.getUserId())) {
                // 设置商业合作品牌
                if (!StringUtils.isEmpty(businessCooperationBrand)) {
                    oppoData.setCommercialCooperationBrands(businessCooperationBrand);
                }
                // 设置3C合作品牌
                if (!StringUtils.isEmpty(threeCCommercialCooperationBrands)) {
                    oppoData.setThreeCCommercialCooperationBrands(threeCCommercialCooperationBrands);
                }

                logger.debug("更新用户{}的品牌信息: 商业合作品牌={}, 3C合作品牌={}",
                           userId, businessCooperationBrand, threeCCommercialCooperationBrands);
                return;
            }
        }
        logger.warn("未找到userId为{}的数据", userId);

    /*    // 没有则新增
        OppoData newData = new OppoData();
        newData.setUserId(userId);
        newData.setCommercialCooperationBrands(businessCooperationBrand);
        newData.setThreeCCommercialCooperationBrands(threeCCommercialCooperationBrands);
        pageData.add(newData);*/
    }

    /**
     * 创建用于分页查询的DocSourceRequest对象
     * @param userIds 用户ID列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return DocSourceRequest对象
     */
    private DocSourceRequest createDocSourceRequestForPage(List<String> userIds, int pageNum, int pageSize) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();

        // 设置extraCondition
        List<Object> extraCondition = new ArrayList<>();
        Map<String, Object> condition = new LinkedHashMap<>();
        condition.put("fieldId", 446);
        condition.put("operator", 0);
        condition.put("value", userIds);
        extraCondition.add(condition);
        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(341497));

        // 设置分页参数 - 这里是关键修复
        docSourceRequest.setPageNum(pageNum);  // 真正的页码
        docSourceRequest.setPageSize(pageSize); // 每页大小

        // 设置其他必要参数
        docSourceRequest.setIndexType("user");
        docSourceRequest.setPath("oppo");
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10);
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>());
        docSourceRequest.setMetricsVo(new ArrayList<>());
        docSourceRequest.setDownloadCount(Collections.singletonList("0"));

        return docSourceRequest;
    }


    /**
     * 创建用于分页查询的PostDocSourceRequest对象
     * @param userIds 用户ID列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return DocSourceRequest对象
     */
    private DocSourceRequest createPostDocSourceRequestForPage(List<String> userIds, int pageNum, int pageSize) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter extraConditionFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        // 设置extraCondition，包含多个条件
        List<Object> extraCondition = new ArrayList<>();

        // 第一个条件：fieldId=267, operator=4，开始时间为今天往前推1年
        Map<String, Object> condition1 = new LinkedHashMap<>();
        condition1.put("fieldId", 267);
        condition1.put("operator", 4);
        condition1.put("value", Collections.singletonList(now.minusYears(1).format(extraConditionFormatter)));
        extraCondition.add(condition1);

        // 第二个条件：fieldId=267, operator=6，结束时间为今天
        Map<String, Object> condition2 = new LinkedHashMap<>();
        condition2.put("fieldId", 267);
        condition2.put("operator", 6);
        condition2.put("value", Collections.singletonList(now.format(extraConditionFormatter)));
        condition2.put("allowNull", 0);
        extraCondition.add(condition2);

        // 第三个条件：fieldId=220, operator=0, value=userIds
        Map<String, Object> condition3 = new LinkedHashMap<>();
        condition3.put("fieldId", 220);
        condition3.put("operator", 0);
        condition3.put("value", userIds);
        extraCondition.add(condition3);

        // 第四个条件：fieldId=312, operator=9, value=["评论"], allowNull=1
        Map<String, Object> condition4 = new LinkedHashMap<>();
        condition4.put("fieldId", 312);
        condition4.put("operator", 9);
        condition4.put("value", Collections.singletonList("评论"));
        condition4.put("allowNull", 1);
        extraCondition.add(condition4);

        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(343907));

        // 设置分页参数
        docSourceRequest.setPageNum(pageNum);
        docSourceRequest.setPageSize(pageSize);

        // 设置其他必要参数
        docSourceRequest.setIndexType("post");
        docSourceRequest.setPath("oppo-post");
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10);
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>());
        docSourceRequest.setMetricsVo(new ArrayList<>());
        docSourceRequest.setDownloadCount(Collections.singletonList("2"));

        // 设置日期范围：从今天往前推半年
        docSourceRequest.setStartDate(now.minusMonths(6).format(dateTimeFormatter));
        docSourceRequest.setEndDate(now.format(dateTimeFormatter));

        return docSourceRequest;
    }


    /**
     *
     * @param
     */

    private DocSourceRequest createUserDocSourceRequestForPage(List<String> douyinLinks, int pageNum, int pageSize) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();
        LocalDateTime now = LocalDateTime.now();
//        DateTimeFormatter extraConditionFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        // 从抖音链接中提取用户ID
        List<String> userIds = new ArrayList<>();
        for (String link : douyinLinks) {
            // 按'/'分割链接，取最后一部分作为用户ID
            String[] parts = link.split("/");
            if (parts.length > 0) {
                userIds.add(parts[parts.length - 1]);
            }
        }

        // 设置extraCondition，包含多个条件
        List<Object> extraCondition = new ArrayList<>();

        // 第一个条件：fieldId=512, operator=0，value为提取的用户ID列表
        Map<String, Object> condition1 = new LinkedHashMap<>();
        condition1.put("fieldId", 512);
        condition1.put("operator", 0);
        condition1.put("value", userIds);
        extraCondition.add(condition1);

        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(341497));

        // 设置分页参数
        docSourceRequest.setPageNum(pageNum);
        docSourceRequest.setPageSize(pageSize);

        // 设置其他必要参数
        docSourceRequest.setIndexType("user");
        docSourceRequest.setPath("oppo");
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10);
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>());
        docSourceRequest.setMetricsVo(new ArrayList<>());
        docSourceRequest.setDownloadCount(Collections.singletonList("1"));

        // 设置日期范围：从今天往前推半年
        docSourceRequest.setStartDate(now.minusMonths(6).format(dateTimeFormatter));
        docSourceRequest.setEndDate(now.format(dateTimeFormatter));

        return docSourceRequest;
    }


    // 使用单独事务保存数据的方法
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveDataInNewTransaction(List<OppoData> oppoDataList) {
        if (!oppoDataList.isEmpty()) {
            logger.info("保存数据到数据库，数量: {}", oppoDataList.size());
            saveAll(oppoDataList);
            logger.info("数据保存完成");

            // 清除缓存，因为可能有新的数据表
            clearLatestTableCache();
        }
    }

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    public boolean checkTableExists(String tableName) {
        try {
            return oppoDataMapper.checkTableExists(tableName) > 0;
        } catch (Exception e) {
            logger.error("检查表是否存在失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取表中的记录数量
     * @param tableName 表名
     * @return 记录数量
     */
    public int getTableRecordCount(String tableName) {
        try {
            return oppoDataMapper.getTableRecordCount(tableName);
        } catch (Exception e) {
            logger.error("获取表记录数量失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查找最新的有数据的周表（带缓存机制）
     * @return 最新有数据的周表名，如果没有找到则返回当前周表名
     */
    public String findLatestTableWithData() {
        try {
            long currentTime = System.currentTimeMillis();

            // 检查缓存是否有效
            if (cachedLatestTableWithData != null &&
                (currentTime - lastCacheTime) < CACHE_DURATION) {
                logger.debug("使用缓存的最新有数据周表: {}", cachedLatestTableWithData);
                return cachedLatestTableWithData;
            }

            logger.debug("开始查找最新的有数据的周表");

            // 获取最近12周的表名（向前查找6周，向后查找6周）
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(12);

            // 按时间倒序排列（最新的在前面）
            tableNames.sort((a, b) -> b.compareTo(a));

            String foundTable = null;
            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (checkTableExists(tableName)) {
                        // 检查表中是否有数据
                        int recordCount = getTableRecordCount(tableName);
                        if (recordCount > 0) {
                            foundTable = tableName;
                            logger.info("找到最新的有数据周表: {}, 记录数: {}", tableName, recordCount);
                            break;
                        } else {
                            logger.debug("表 {} 存在但无数据，继续查找", tableName);
                        }
                    } else {
                        logger.debug("表 {} 不存在，继续查找", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("检查表 {} 时出错: {}", tableName, e.getMessage());
                }
            }

            // 如果没有找到有数据的表，使用当前周表
            if (foundTable == null) {
                foundTable = WeeklyTableUtil.getCurrentWeekTableName();
                logger.warn("未找到有数据的周表，使用当前周表: {}", foundTable);
            }

            // 更新缓存
            cachedLatestTableWithData = foundTable;
            lastCacheTime = currentTime;

            return foundTable;

        } catch (Exception e) {
            logger.error("查找最新有数据周表失败: {}", e.getMessage(), e);
            // 出错时返回当前周表作为兜底
            String currentWeekTable = WeeklyTableUtil.getCurrentWeekTableName();
            logger.info("查找失败，使用当前周表作为兜底: {}", currentWeekTable);
            return currentWeekTable;
        }
    }

    /**
     * 清除缓存（当有新数据插入时调用）
     */
    public void clearLatestTableCache() {
        cachedLatestTableWithData = null;
        lastCacheTime = 0;
        logger.debug("已清除最新有数据周表的缓存");
    }

    /**
     * 优化的分页查询OPPO数据（支持多条件查询）
     * @param request 查询请求参数
     * @param tableName 表名
     * @return 分页响应结果
     */
    public PageResponse<OppoData> queryOppoDataByPageOptimized(PageQueryRequest request, String tableName) {
        try {
            // 注释掉详细的查询条件打印，避免泄露业务数据
            // logger.info("开始优化分页查询OPPO数据，表名: {}, 查询条件: {}", tableName, request);
            logger.info("开始优化分页查询OPPO数据，表名: {}", tableName);

            // 先查询总数
            int totalCount = oppoDataMapper.countOptimized(tableName, request);

            if (totalCount == 0) {
                logger.info("表 {} 中没有找到匹配的数据", tableName);
                return PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
            }

            // 计算分页参数
            int offset = (request.getPageNum() - 1) * request.getPageSize();

            // 分页查询数据
            List<OppoData> dataList = oppoDataMapper.selectPageOptimized(tableName, request, offset, request.getPageSize());
            if(CollectionUtils.isEmpty(dataList)){
                logger.info("表 {} 中没有找到匹配的数据", tableName);
                return PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
            }
            //90_非商单互动量
            //calculateNonCommercialInteractionVolume(dataList);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());

            logger.info("优化分页查询完成，表: {}, 总计: {} 条，当前页: {} 条", tableName, totalCount, dataList.size());

            PageResponse<OppoData> response = new PageResponse<>();
            response.setPageNum(request.getPageNum());
            response.setPageSize(request.getPageSize());
            response.setTotal((long) totalCount);
            response.setTotalPages(totalPages);
            response.setData(dataList != null ? dataList : new ArrayList<>());

            return response;

        } catch (Exception e) {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.error("优化分页查询OPPO数据失败: tableName={}, request={}, error={}", tableName, request, e.getMessage(), e);
            logger.error("优化分页查询OPPO数据失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
        }
    }



    public static void calculateNonCommercialInteractionVolume(List<OppoData> dataList) {
        dataList.forEach(data -> {

            //90_非商单互动量
            long forward90Days = parseLongSafely(data.getNonCommercialAverageForward90Days());
            long comment90Days = parseLongSafely(data.getNonCommercialAverageComment90Days());
            long like90Days = parseLongSafely(data.getNonCommercialAverageLike90Days());
            data.setNonCommercialInteractionVolume90Days(String.valueOf(forward90Days + comment90Days + like90Days));

            //90_商单互动量
            long commercialForward90Days = parseLongSafely(data.getCommercialAverageForward90Days());
            long commercialComment90Days = parseLongSafely(data.getCommercialAverageComment90Days());
            long commercialLike90Days = parseLongSafely(data.getCommercialAverageLike90Days());
            data.setCommercialInteractionVolume90Days(String.valueOf(commercialForward90Days + commercialComment90Days + commercialLike90Days));


            //30_非商单互动量
            long forward30Days = parseLongSafely(data.getNonCommercialAverageForward30Days());
            long comment30Days = parseLongSafely(data.getNonCommercialAverageComment30Days());
            long like30Days = parseLongSafely(data.getNonCommercialAverageLike30Days());
            data.setNonCommercialInteractionVolume30Days(String.valueOf(forward30Days + comment30Days + like30Days));


            //30_商单互动量
            long commercialForward30Days = parseLongSafely(data.getCommercialAverageForward30Days());
            long commercialComment30Days = parseLongSafely(data.getCommercialAverageComment30Days());
            long commercialLike30Days = parseLongSafely(data.getCommercialAverageLike30Days());
            data.setCommercialInteractionVolume30Days(String.valueOf(commercialForward30Days + commercialComment30Days + commercialLike30Days));

            //近30天粉丝增长数 = 粉丝数*涨粉率
            long followersCount = parseLongSafely(data.getFollowersCount());
            double fansIncrementRate30Days = parseDoubleSafely(data.getFansIncrementRate30Days());
            data.setFansGrowthCountLast30Days(String.valueOf((long) (followersCount * fansIncrementRate30Days)));


            //粉丝画像
            Object distribution = data.getObjectStarObjectFansDistribution();
            //转成JSONObject
            if (distribution != null) {
                JSONObject fansDistribution = JSONObject.parseObject(distribution.toString());
                //粉丝性别分布
                if (fansDistribution.containsKey("object_genderDistribution")) {
                    data.setFansGenderDistribution(fansDistribution.getString("object_genderDistribution"));
                }
                //粉丝年龄分布
                if (fansDistribution.containsKey("object_ageDistribution")) {
                    data.setFansAgeDistribution(fansDistribution.getString("object_ageDistribution"));
                }
                //粉丝地域分布
            }

        });
    }

    private static double parseDoubleSafely(String input) {
        // 首先检查输入是否为 null
        if (input == null) {
            return 0;
        }
        try {
            // 尝试将输入的字符串转换为 double 类型
            return Double.parseDouble(input);
        } catch (NumberFormatException e) {
            // 如果转换过程中抛出 NumberFormatException 异常，说明输入的字符串不是有效的数字
            // 此时返回默认值 0
            return 0;
        }
    }

    private static long parseLongSafely(String value) {
        try {
            return value != null ? Long.parseLong(value) : 0L;
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    /**
     * 安全地将对象转换为 JSON 字符串
     * @param obj 要转换的对象
     * @return JSON 字符串，如果转换失败则返回 null
     */
    private static String toJsonStringSafely(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            // 如果已经是字符串，检查是否是有效的 JSON
            if (obj instanceof String) {
                String str = (String) obj;
                if (str.trim().isEmpty()) {
                    return null;
                }
                // 尝试解析以验证是否是有效的 JSON
                JSON.parse(str);
                return str;
            }

            // 将对象转换为 JSON 字符串
            return JSON.toJSONString(obj);

        } catch (Exception e) {
            logger.warn("转换对象为JSON字符串失败: {}, 对象类型: {}", e.getMessage(), obj.getClass().getSimpleName());
            return null;
        }
    }

    /**
     * 查找有画像数据的用户
     * @param tableName 表名
     * @param limit 限制数量
     * @return 用户列表
     */
    public List<OppoData> findUsersWithPortraitData(String tableName, int limit) {
        try {
            return oppoDataMapper.selectUsersWithPortraitData(tableName, limit);
        } catch (Exception e) {
            logger.error("查找有画像数据的用户失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据用户ID查询用户数据
     * @param tableName 表名
     * @param userId 用户ID
     * @return 用户数据
     */
    public OppoData findByUserId(String tableName, String userId) {
        try {
            return oppoDataMapper.selectByUserId(tableName, userId);
        } catch (Exception e) {
            logger.error("根据用户ID查询用户数据失败: tableName={}, userId={}, error={}", tableName, userId, e.getMessage(), e);
            return null;
        }
    }

}
