package data.oppodataapi.service;

import data.oppodataapi.dto.DocSourceRequest;
import data.oppodataapi.dto.QueryRequest;
import data.oppodataapi.dto.PageQueryRequest;
import data.oppodataapi.dto.PageResponse;
import data.oppodataapi.entity.OppoData;
import data.oppodataapi.mapper.OppoDataMapper;
import data.oppodataapi.util.WeeklyTableUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据中心服务类
 */
@Service
public class OppoDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(OppoDataService.class);
    
    @Value("${datacenter.api.base-url:https://dag-api.tarsocial.com}")
    private String baseUrl;
    
    @Autowired
    private TokenService tokenService;
    //这个token 从数据库中获取
    
    @Value("${datacenter.api.organization-id}")
    private String organizationId;

    private final RestTemplate restTemplate;

    @Autowired
    private TokenValidationService tokenValidationService;

    @Autowired
    private OppoDataMapper oppoDataMapper;

    // 缓存最新有数据的表名，避免频繁查询
    private volatile String cachedLatestTableWithData = null;
    private volatile long lastCacheTime = 0;
    private static final long CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

    public OppoDataService() {
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 查询数据中心数据
     * @param request 查询请求
     * @return 查询结果
     */
    public Object queryDataCenter(QueryRequest request) {
        String url = baseUrl + "/dc/dataCenter/query";
        
        HttpHeaders headers = createHeaders();
        HttpEntity<QueryRequest> entity = new HttpEntity<>(request, headers);
        
        try {
            logger.info("调用数据中心查询接口: {}", url);
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.debug("请求参数: {}", request);
            
            ResponseEntity<Object> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                Object.class
            );
            
            logger.info("数据中心查询接口调用成功");
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用数据中心查询接口失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用数据中心查询接口失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询文档源数据
     * @param request 文档源查询请求
     * @return 查询结果
     */
    public Object queryDocSource(DocSourceRequest request) {
        String url = baseUrl + "/dc/extend/doc-source";
        HttpHeaders headers  = createHeaders();
        HttpEntity<DocSourceRequest> entity = new HttpEntity<>(request, headers);
        
        try {
            logger.info("调用文档源查询接口: {}", url);
             logger.debug("请求参数: {}", request);

            ResponseEntity<Object> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                Object.class
            );
            List<String> tid = response.getHeaders().get("request-id");
            logger.info("请求ID: {}", tid.get(0));
            logger.info("文档源查询接口调用成功");
            return response.getBody();
            
        } catch (Exception e) {
            logger.error("调用文档源查询接口失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用文档源查询接口失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建请求头
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        // 从数据库获取Token
        String token = tokenService.getDataCenterToken();
        headers.set("Authorization", "Bearer " + token);
        headers.set("organizationid", organizationId);
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        return headers;
    }

    /**
     * 创建请求头
     * @return HTTP请求头
     */
    private HttpHeaders createTokenHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        headers.set("Authorization", "Bearer " + token);
        headers.set("organizationid", organizationId);
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        return headers;
    }



    /**
     * 将查询结果转换为OppoData列表
     * @param result 查询结果
     * @return OppoData列表
     */
    @SuppressWarnings("unchecked")
    public List<OppoData> convertToOppoDataList(Object result) {
        List<OppoData> oppoDataList = new ArrayList<>();

        if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            if (resultMap.containsKey("data") && resultMap.get("data") != null) {
                List<Map<String, Object>> dataList = List.of();
                if (resultMap.containsKey("data")) {
                    Object data = resultMap.get("data");
                    // 使用instanceof分情况处理，并添加泛型类型检查
                    if (data instanceof List) {
                        // 强制转换前先检查列表元素是否为Map类型
                        List<?> rawList = (List<?>) data;
                        if (rawList.stream().allMatch(item -> item instanceof Map)) {
                            // 安全转换为List<Map<String, Object>>
                            dataList = (List<Map<String, Object>>) (List<?>) rawList;
                        } else {
                            logger.error("列表中存在非Map类型的数据");
                        }
                    } else if (data instanceof Map) {
                        // 单条数据处理逻辑
                        @SuppressWarnings("unchecked")
                        Map<String, Object> singleData = (Map<String, Object>) data;
                        dataList = Collections.singletonList(singleData);
                    } else {
                        logger.error("data字段类型错误，期望List或Map");
                    }
                } else {
                    logger.error("缺少data字段");
                }
                if (dataList.isEmpty()) {
                    logger.info("没有查询到数据");
                    return Collections.emptyList();
                }

                Map<String, Object> stringObjectMap = dataList.getFirst();
                ArrayList rows = (ArrayList) stringObjectMap.get("rows");

                rows.forEach(item -> {
                    Map<String, Object> sourceMap = (Map<String, Object>) ((LinkedHashMap) item).get("source");

                    if (sourceMap == null) {
                        logger.warn("source为空，跳过此条记录");
                        return;
                    }

                    OppoData oppoData = new OppoData();

                    // 从source中获取基本信息
                    if (sourceMap.containsKey("tkw_nickname")) {
                        oppoData.setUserNickname(String.valueOf(sourceMap.get("tkw_nickname")));
                    }
                    if (sourceMap.containsKey("kw_userId")) {
                        oppoData.setUserId(String.valueOf(sourceMap.get("kw_userId")));
                    }
                    if (sourceMap.containsKey("kw_platformUserId")) {
                        oppoData.setDouyinId(String.valueOf(sourceMap.get("kw_platformUserId")));
                    }
                    if (sourceMap.containsKey("kw_kolId")) {
                        oppoData.setStarMapId(String.valueOf(sourceMap.get("kw_kolId")));
                    }
                    if (sourceMap.containsKey("kw_avatar")) {
                        oppoData.setUserAvatar(String.valueOf(sourceMap.get("kw_avatar")));
                    }
                    if (sourceMap.containsKey("tx_description")) {
                        oppoData.setTalentDescription(String.valueOf(sourceMap.get("tx_description")));
                    }

                    // 链接信息
                    if (sourceMap.containsKey("kw_kolId")) {
                        oppoData.setStarMapLink("https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/" + sourceMap.get("kw_kolId"));
                    }
                    if (sourceMap.containsKey("secuid")) {
                        oppoData.setDouyinLink("https://www.douyin.com/user/" + sourceMap.get("secuid"));
                    }

                    // 粉丝数
                    if (sourceMap.containsKey("long_followersCount")) {
                        oppoData.setFollowersCount(String.valueOf(sourceMap.get("long_followersCount")));
                    }

                    // MCN信息
                    if (sourceMap.containsKey("object_star.kw_mcnName")) {
                        oppoData.setMcn(String.valueOf(sourceMap.get("object_star.kw_mcnName")));
                    }

                    // 价格信息 - 使用扁平化的键
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price1_20")) {
                        oppoData.setShortVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price1_20")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price20_60")) {
                        oppoData.setLongVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price20_60")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_price60")) {
                        oppoData.setExtraLongVideoPrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_price60")));
                    }
                    if (sourceMap.containsKey("object_star.object_priceInfos.long_priceImageArticle")) {
                        oppoData.setImageArticlePrice(String.valueOf(sourceMap.get("object_star.object_priceInfos.long_priceImageArticle")));
                    }

                    // 预期CPM和CPE
                    if (sourceMap.containsKey("object_star.double_expectedStarCpm20_60")) {
                        oppoData.setExpectedCpm20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpm20_60")));
                    }
                    if (sourceMap.containsKey("object_star.double_expectedStarCpe20_60")) {
                        oppoData.setExpectedCpe20To60Video(String.valueOf(sourceMap.get("object_star.double_expectedStarCpe20_60")));
                    }
                    if (sourceMap.containsKey("object_star.long_expectedPlayNum")) {
                        oppoData.setExpectedPlayCount20To60Video(String.valueOf(sourceMap.get("object_star.long_expectedPlayNum")));
                    }

                    // 爆文率
                    if (sourceMap.containsKey("object_star.double_burstRate")) {
                        oppoData.setViralRate(String.valueOf(sourceMap.get("object_star.double_burstRate")));
                    }

                    // 达人身份标签
                    if (sourceMap.containsKey("object_star.kw_statusTag")) {
                        oppoData.setTalentStatusTag(String.valueOf(sourceMap.get("object_star.kw_statusTag")));
                    }
                    if (sourceMap.containsKey("object_star.kw_socialStatusTag")) {
                        oppoData.setTalentSocialStatusTag(String.valueOf(sourceMap.get("object_star.kw_socialStatusTag")));
                    }
                    if (sourceMap.containsKey("kw_tags")) {
                        oppoData.setTalentType(String.valueOf(sourceMap.get("kw_tags")));
                    }

                    // 内容主题
                    if (sourceMap.containsKey("object_star.kw_contentTheme")) {
                        oppoData.setContentTheme(String.valueOf(sourceMap.get("object_star.kw_contentTheme")));
                    }
                    if (sourceMap.containsKey("kw_industryTags")) {
                        oppoData.setIndustryTags(String.valueOf(sourceMap.get("kw_industryTags")));
                    }

                    // 内容一级标签_商单
                    if (sourceMap.containsKey("object_star.flattened_videoTouch.video_touch")) {
                        oppoData.setContentFirstLevelTagForCommercial(String.valueOf(sourceMap.get("object_star.flattened_videoTouch.video_touch")));
                    }

                    // 月连接用户数及相关数据
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_connect.long_count")) {
                        oppoData.setMonthlyConnectedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_connect.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_connect.double_relative_ratio")) {
                        oppoData.setMonthlyConnectedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_connect.double_relative_ratio")));
                    }

                    // 月了解用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_understand.long_count")) {
                        oppoData.setMonthlyUnderstoodUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_understand.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_understand.double_relative_ratio")) {
                        oppoData.setMonthlyUnderstoodUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_understand.double_relative_ratio")));
                    }

                    // 月兴趣用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_interest.long_count")) {
                        oppoData.setMonthlyInterestedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_interest.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_interest.double_relative_ratio")) {
                        oppoData.setMonthlyInterestedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_interest.double_relative_ratio")));
                    }

                    // 月喜欢用户数
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_like.long_count")) {
                        oppoData.setMonthlyLikedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_like.long_count")));
                    }
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_like.double_relative_ratio")) {
                        oppoData.setMonthlyLikedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_like.double_relative_ratio")));
                    }

                    //月追随用户数30天环比
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_follow.long_count")) {
                        oppoData.setMonthlyFollowedUserCount(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_follow.long_count")));
                    }

                    //月追随用户数30天环比
                    if (sourceMap.containsKey("object_star.object_linkStruct.object_follow.double_relative_ratio")) {
                        oppoData.setMonthlyFollowedUserCount30DayRatio(String.valueOf(sourceMap.get("object_star.object_linkStruct.object_follow.double_relative_ratio")));
                    }


                    // 添加到结果列表
                    oppoDataList.add(oppoData);
                });
            }
        }

        return oppoDataList;
    }


    public void saveAll(List<OppoData> oppoDataList) {
        try {
            if (oppoDataList == null || oppoDataList.isEmpty()) {
                logger.warn("数据列表为空，无需保存");
                return;
            }

            // 获取当前周的表名
            String currentWeekTable = WeeklyTableUtil.getCurrentWeekTableName();
            logger.info("开始批量保存数据到表 {}，共 {} 条记录", currentWeekTable, oppoDataList.size());

            // 确保当前周表存在
            ensureWeeklyTableExists(currentWeekTable);

            // 分批保存，避免SQL语句过长
            int batchSize = 100;
            for (int i = 0; i < oppoDataList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, oppoDataList.size());
                List<OppoData> batch = oppoDataList.subList(i, endIndex);

                logger.debug("保存第 {} 批数据到表 {}，共 {} 条", (i / batchSize) + 1, currentWeekTable, batch.size());
                int insertCount = oppoDataMapper.batchInsertToTable(currentWeekTable, batch);
                logger.debug("第 {} 批数据保存完成，实际插入 {} 条", (i / batchSize) + 1, insertCount);
            }

            logger.info("批量保存数据完成，共处理 {} 条记录到表 {}", oppoDataList.size(), currentWeekTable);

        } catch (Exception e) {
            logger.error("批量保存数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确保周表存在，如果不存在则创建
     * @param tableName 表名
     */
    private void ensureWeeklyTableExists(String tableName) {
        try {
            if (oppoDataMapper.checkTableExists(tableName) == 0) {
                logger.info("表 {} 不存在，开始创建", tableName);

                // 使用第一周的表作为模板
                String templateTable = "oppo_data_2025_01";
                oppoDataMapper.createWeeklyTable(tableName, templateTable);

                logger.info("表 {} 创建成功", tableName);
            } else {
                logger.debug("表 {} 已存在", tableName);
            }
        } catch (Exception e) {
            logger.error("创建表 {} 失败: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("创建周表失败: " + e.getMessage(), e);
        }
    }

    public List<OppoData> getDataByUserId(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                logger.warn("用户ID为空，无法查询");
                return new ArrayList<>();
            }

            logger.debug("开始从周表查询用户ID: {}", userId);

            // 查询最近4周的数据
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(4);
            List<OppoData> allResults = new ArrayList<>();

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        List<OppoData> tableResults = oppoDataMapper.selectByUserIdFromTable(tableName, userId);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("周表查询完成，用户ID: {} 总共返回 {} 条记录", userId, allResults.size());
            return allResults;

        } catch (Exception e) {
            logger.error("根据用户ID查询数据失败: userId={}, error={}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public OppoData getDataByStarMapId(String starMapId) {
        try {
            if (!StringUtils.hasText(starMapId)) {
                logger.warn("星图ID为空，无法查询");
                return null;
            }

            logger.debug("开始从数据库查询星图ID: {}", starMapId);
            OppoData result = oppoDataMapper.selectByStarMapId(starMapId);

            logger.debug("数据库查询完成，星图ID: {} 返回结果: {}", starMapId, result != null ? "找到数据" : "未找到数据");
            return result;

        } catch (Exception e) {
            logger.error("根据星图ID查询数据失败: starMapId={}, error={}", starMapId, e.getMessage(), e);
            return null;
        }
    }

    public List<OppoData> getAllData() {
        try {
            logger.debug("开始从数据库查询所有数据");
            List<OppoData> result = oppoDataMapper.selectList(null);

            if (result == null) {
                result = new ArrayList<>();
            }

            logger.debug("数据库查询完成，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            logger.error("查询所有数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 分页查询OppoData数据（先查数据库，没有则调用API）
     * @param request 分页查询请求
     * @return 分页响应结果
     */
    public PageResponse<OppoData> queryOppoDataByPage(PageQueryRequest request) {
        try {
            // 校验Token
            tokenValidationService.validateTokenOrThrow(request.getToken());

            logger.info("开始分页查询OppoData，页码: {}, 页大小: {}", request.getPageNum(), request.getPageSize());

            // 先从数据库查询
            PageResponse<OppoData> dbResult = queryOppoDataFromDatabase(request);

            // 如果数据库中有数据，直接返回
            if (dbResult.getTotal() > 0) {
                logger.info("从数据库查询到数据，返回 {} 条记录，总计 {} 条",
                           dbResult.getData().size(), dbResult.getTotal());
                return dbResult;
            }

            logger.info("数据库中没有数据，调用API接口查询");

            // 数据库中没有数据，调用API接口
            DocSourceRequest docSourceRequest = buildDocSourceRequest(request);
            Object result = queryDocSource(docSourceRequest);
            List<OppoData> oppoDataList = convertToOppoDataList(result);

            // 如果API返回了数据，保存到数据库
            if (!oppoDataList.isEmpty()) {
                logger.info("API查询到 {} 条数据，保存到数据库", oppoDataList.size());
                saveAll(oppoDataList);
            }

            // 计算分页信息
            int pageNum = request.getPageNum();
            int pageSize = request.getPageSize();
            long total = oppoDataList.size();

            // 计算分页数据
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, oppoDataList.size());

            List<OppoData> pageData = new ArrayList<>();
            if (startIndex < oppoDataList.size()) {
                pageData = oppoDataList.subList(startIndex, endIndex);
            }

            logger.info("API分页查询完成，返回 {} 条记录", pageData.size());

            return PageResponse.of(pageNum, pageSize, total, pageData);

        } catch (Exception e) {
            logger.error("分页查询OppoData失败: {}", e.getMessage(), e);
            throw new RuntimeException("分页查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从数据库分页查询OppoData数据
     * @param request 分页查询请求
     * @return 分页响应结果
     */
    private PageResponse<OppoData> queryOppoDataFromDatabase(PageQueryRequest request) {
        try {
            // 计算分页参数
            int pageNum = request.getPageNum();
            int pageSize = request.getPageSize();
            int offset = (pageNum - 1) * pageSize;

            List<OppoData> allDataList = new ArrayList<>();

            if (StringUtils.hasText(request.getUserIds())) {
                // 有用户ID条件：批量查询所有表中符合userIds的数据
                logger.debug("根据用户ID批量查询数据: {}", request.getUserIds());
                allDataList = queryDataByUserIds(request.getUserIds());
            } else {
                // 无用户ID条件：查询最近两周的所有数据
                logger.debug("查询最近两周的所有数据");
                allDataList = queryRecentTwoWeeksAllData();
            }

            // 计算总数
            long total = allDataList.size();

            // 手动分页
            List<OppoData> pageDataList = new ArrayList<>();
            if (total > 0) {
                int startIndex = Math.min(offset, allDataList.size());
                int endIndex = Math.min(offset + pageSize, allDataList.size());
                if (startIndex < allDataList.size()) {
                    pageDataList = allDataList.subList(startIndex, endIndex);
                }
            }

            logger.debug("数据库查询结果: 总计 {} 条，当前页 {} 条", total, pageDataList.size());

            return PageResponse.of(pageNum, pageSize, total, pageDataList);

        } catch (Exception e) {
            logger.error("数据库分页查询失败: {}", e.getMessage(), e);
            // 返回空结果，让程序继续调用API
            return PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
        }
    }

    /**
     * 根据用户ID列表批量查询数据（查询所有表）
     * @param userIds 用户ID列表（逗号分隔）
     * @return OppoData列表
     */
    private List<OppoData> queryDataByUserIds(String userIds) {
        List<OppoData> allResults = new ArrayList<>();

        try {
            // 解析用户ID列表
            String[] userIdArray = userIds.split(",");
            List<String> userIdList = new ArrayList<>();
            for (String userId : userIdArray) {
                String trimmedUserId = userId.trim();
                if (StringUtils.hasText(trimmedUserId)) {
                    userIdList.add(trimmedUserId);
                }
            }

            if (userIdList.isEmpty()) {
                logger.warn("用户ID列表为空");
                return allResults;
            }

            // 获取最近4周的表名（扩大查询范围以确保数据完整性）
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(4);

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        // 批量查询该表中所有符合条件的用户数据
                        List<OppoData> tableResults = queryUserDataFromTable(tableName, userIdList);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("批量用户ID查询完成，总共返回 {} 条记录", allResults.size());

        } catch (Exception e) {
            logger.error("批量查询用户数据失败: userIds={}, error={}", userIds, e.getMessage(), e);
        }

        return allResults;
    }

    /**
     * 查询最近两周的所有数据
     * @return OppoData列表
     */
    private List<OppoData> queryRecentTwoWeeksAllData() {
        List<OppoData> allResults = new ArrayList<>();

        try {
            // 获取最近两周的表名
            List<String> tableNames = WeeklyTableUtil.getRecentWeekTableNames(2);

            for (String tableName : tableNames) {
                try {
                    // 检查表是否存在
                    if (oppoDataMapper.checkTableExists(tableName) > 0) {
                        // 查询该表的所有数据
                        List<OppoData> tableResults = oppoDataMapper.selectAllFromTable(tableName);
                        if (tableResults != null && !tableResults.isEmpty()) {
                            allResults.addAll(tableResults);
                            logger.debug("从表 {} 查询到 {} 条记录", tableName, tableResults.size());
                        }
                    } else {
                        logger.debug("表 {} 不存在，跳过查询", tableName);
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("最近两周数据查询完成，总共返回 {} 条记录", allResults.size());

        } catch (Exception e) {
            logger.error("查询最近两周数据失败: {}", e.getMessage(), e);
        }

        return allResults;
    }

    /**
     * 从指定表中查询用户数据
     * @param tableName 表名
     * @param userIds 用户ID列表
     * @return OppoData列表
     */
    private List<OppoData> queryUserDataFromTable(String tableName, List<String> userIds) {
        try {
            if (userIds == null || userIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 使用批量查询方法
            List<OppoData> results = oppoDataMapper.selectByUserIdsFromTable(tableName, userIds);
            return results != null ? results : new ArrayList<>();

        } catch (Exception e) {
            logger.error("从表 {} 查询用户数据失败: userIds={}, error={}", tableName, userIds, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建DocSourceRequest
     * @param request 分页查询请求
     * @return DocSourceRequest
     */
    private DocSourceRequest buildDocSourceRequest(PageQueryRequest request) {
        DocSourceRequest docSourceRequest = new DocSourceRequest();

        // 设置extraCondition - 确保字段顺序与正常参数一致
        List<Object> extraCondition = new ArrayList<>();
        Map<String, Object> condition = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
        condition.put("fieldId", 446);
        condition.put("operator", 0);
        String userIds = request.getUserIds();
        // 直接使用逗号分隔的字符串，不添加引号
        List<String> userIdList = Arrays.stream(userIds.split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        condition.put("value", userIdList);
        extraCondition.add(condition);
        docSourceRequest.setExtraCondition(extraCondition);
        docSourceRequest.setMainPartyId(Collections.singletonList(341497));

        // 设置其他必要参数 - 确保与正常参数一致
        docSourceRequest.setIndexType("user");
        docSourceRequest.setPath("oppo");
        docSourceRequest.setPageNum(request.getPageNum() != null ? request.getPageNum() : 1);
        docSourceRequest.setPageSize(request.getPageSize() != null ? request.getPageSize() : 100);
        docSourceRequest.setFlatSource(true);
        docSourceRequest.setPlatformIds(Collections.singletonList(44));
        docSourceRequest.setHighLight(true);
        docSourceRequest.setArrayFlatSource(true);
        docSourceRequest.setTop(10); // 使用10而不是100
        docSourceRequest.setTitle("总计");
        docSourceRequest.setColumnVo(null);
        docSourceRequest.setRowVo(new ArrayList<>()); // 使用空列表而不是null
        docSourceRequest.setMetricsVo(new ArrayList<>()); // 使用空列表而不是null
        docSourceRequest.setToken(request.getToken());

        // 设置downloadCount
        docSourceRequest.setDownloadCount(Collections.singletonList("0"));
        return docSourceRequest;
    }
}
