package data.oppodataapi.service;

import data.oppodataapi.entity.OppoUserBatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 每月月中和月末自动跑数定时任务
 */
@Component
public class OppoUserBatchJob {
    private static final Logger logger = LoggerFactory.getLogger(OppoUserBatchJob.class);

    @Autowired
    private OppoUserService oppoUserService;
    @Autowired
    private OppoDataService oppoDataService;

    /**
     * 每月15日和每月最后一天的凌晨1点执行
     * cron表达式：秒 分 时 日 月 星期
     * "0 0 1 15,L * ?" 表示每月15日和每月最后一天的1:00
     */
    @Scheduled(cron = "0 0 1 15,L * ?")
    public void runBatchDataJob() {
        logger.info("[定时任务] 开始执行月中/月末批次跑数任务...");
        try {
            // 查询最新成功的批次
            OppoUserBatch latestBatch = oppoUserService.getLatestSuccessBatch();
            if (latestBatch == null) {
                logger.warn("未找到任何成功的批次，跳过本次跑数任务");
                return;
            }
            String batchId = latestBatch.getBatchId();
            logger.info("最新批次ID: {}，批次名: {}", batchId, latestBatch.getBatchName());

            // 跑数逻辑：调用Service层方法
            // 获取用户ID列表
            java.util.List<String> userIds = oppoUserService.getUserIdsByBatchId(batchId);
            java.util.List<data.oppodataapi.entity.OppoData> result = oppoDataService.createQueryQuarkData(userIds);
            logger.info("批次跑数完成，用户数: {}，返回数据条数: {}", userIds.size(), result.size());
        } catch (Exception e) {
            logger.error("批次跑数定时任务异常", e);
        }
    }
} 