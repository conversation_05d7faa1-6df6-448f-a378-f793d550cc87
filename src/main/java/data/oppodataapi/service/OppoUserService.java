package data.oppodataapi.service;

import data.oppodataapi.dto.OppoUserUploadRequest;
import data.oppodataapi.dto.OppoUserUploadResponse;
import data.oppodataapi.entity.OppoUser;
import data.oppodataapi.entity.OppoUserBatch;
import data.oppodataapi.mapper.OppoUserMapper;
import data.oppodataapi.mapper.OppoUserBatchMapper;
import data.oppodataapi.util.UserWeeklyTableUtil;
import data.oppodataapi.util.ExcelUtil;
import data.oppodataapi.util.BatchIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.List;

/**
 * OPPO用户服务
 */
@Service
public class OppoUserService {
    
    private static final Logger logger = LoggerFactory.getLogger(OppoUserService.class);
    
    @Autowired
    private OppoUserMapper oppoUserMapper;

    @Autowired
    private OppoUserBatchMapper oppoUserBatchMapper;

    @Autowired
    private TokenValidationService tokenValidationService;
    
    /**
     * 批量上传用户数据
     * @param request 上传请求
     * @return 上传响应
     */
    public OppoUserUploadResponse uploadUsers(OppoUserUploadRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        OppoUserUploadResponse response = new OppoUserUploadResponse();
        response.setStartTime(startTime);
        
        // 生成批次ID和批次名称
        String batchId = BatchIdGenerator.generateApiBatchId();
        String batchName = BatchIdGenerator.generateBatchName("API", "API上传");

        // 创建批次记录
        OppoUserBatch batch = new OppoUserBatch(batchId, batchName, "API");
        batch.setCreatedBy("SYSTEM");

        // 设置批次信息到响应中
        response.setBatchId(batchId);
        response.setBatchName(batchName);
        response.setUploadSource("API");

        try {
            // 保存批次记录
            oppoUserBatchMapper.insert(batch);
            logger.info("创建批次记录: {}", batchId);

            // 校验Token
            if (tokenValidationService != null) {
                tokenValidationService.validateTokenOrThrow(request.getToken());
            } else {
                logger.warn("TokenValidationService 未注入，跳过Token校验");
            }

            // 参数校验
            if (request.getUsers() == null || request.getUsers().isEmpty()) {
                batch.setFailed("用户数据列表不能为空");
                oppoUserBatchMapper.updateById(batch);
                response.setTotalCount(0);
                response.setFailedCount(0);
                response.setSuccessCount(0);
                throw new RuntimeException("用户数据列表不能为空");
            }

            // 获取当前周表名
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            response.setTableName(currentWeekTable);

            // 确保当前周表存在
            ensureWeeklyTableExists(currentWeekTable);

            logger.info("开始批量上传用户数据到表 {}，共 {} 条记录，批次ID: {}", currentWeekTable, request.getUsers().size(), batchId);
            
            // 转换为实体对象，并设置批次信息
            List<OppoUser> userList = convertToOppoUsersWithBatch(request.getUsers(), batchId, batchName, "API");
            response.setTotalCount(userList.size());

            // 更新批次记录的总数
            batch.setTotalCount(userList.size());
            
            // 分批处理
            int batchSize = request.getBatchSize() != null ? request.getBatchSize() : 100;
            int successCount = 0;
            int failedCount = 0;
            List<OppoUserUploadResponse.FailedRecord> failedRecords = new ArrayList<>();
            
            for (int i = 0; i < userList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, userList.size());
                List<OppoUser> batchs = userList.subList(i, endIndex);
                
                try {
                    boolean isOverwriteMode = request.getOverwrite() != null ? request.getOverwrite() : false;
                    logger.debug("处理第 {} 批用户数据，共 {} 条，覆盖模式: {}", (i / batchSize) + 1, batchs.size(), isOverwriteMode);

                    // 确保所有记录都有正确的时间戳
                    LocalDateTime batchTime = LocalDateTime.now();
                    for (OppoUser user : batchs) {
                        if (user.getCreateTime() == null) {
                            user.setCreateTime(batchTime);
                        }
                        // 总是更新 update_time 为当前时间
                        user.setUpdateTime(batchTime);
                    }

                    int insertCount;
                    if (isOverwriteMode) {
                        // 覆盖模式：使用 ON DUPLICATE KEY UPDATE
                        insertCount = oppoUserMapper.batchInsertToTable(currentWeekTable, batchs);
                        // 修正计数：ON DUPLICATE KEY UPDATE 在更新时返回2，我们需要按实际记录数计算
                        insertCount = Math.min(insertCount, batchs.size());
                    } else {
                        // 非覆盖模式：使用 INSERT IGNORE（重复数据会被忽略）
                        insertCount = oppoUserMapper.batchInsertOnlyToTable(currentWeekTable, batchs);
                    }

                    successCount += insertCount;
                    logger.debug("第 {} 批用户数据处理完成，实际处理 {} 条（覆盖模式: {}）", (i / batchSize) + 1, insertCount, isOverwriteMode);
                    
                } catch (Exception e) {
                    logger.error("第 {} 批用户数据处理失败: {}", (i / batchSize) + 1, e.getMessage(), e);
                    failedCount += batchs.size();
                    
                    // 记录失败详情
                    for (OppoUser user : batchs) {
                        failedRecords.add(new OppoUserUploadResponse.FailedRecord(
                            user.getDouyinId(), user.getStarMapId(), e.getMessage()));
                    }
                }
            }
            
            response.setSuccessCount(successCount);
            response.setFailedCount(failedCount);
            response.setFailedRecords(failedRecords);

            // 更新批次状态
            batch.setSuccessCount(successCount);
            batch.setFailedCount(failedCount);
           /* if (failedCount == 0) {
                batch.setSuccess();
            } else if (successCount == 0) {
                batch.setFailed("所有数据处理失败");
            } else {
                batch.setPartial();
            }*/
            batch.setCompletedTime(LocalDateTime.now());
            oppoUserBatchMapper.updateById(batch);

            logger.info("批量上传用户数据完成，总计 {} 条，成功 {} 条，失败 {} 条，批次ID: {}",
                       response.getTotalCount(), successCount, failedCount, batchId);
            
        } catch (Exception e) {
            logger.error("批量上传用户数据失败: {}", e.getMessage(), e);
            response.setFailedCount(response.getTotalCount() != null ? response.getTotalCount() : 0);
            response.setSuccessCount(0);

            // 更新批次状态为失败
            try {
                batch.setFailed("批量上传失败: " + e.getMessage());
                batch.setCompletedTime(LocalDateTime.now());
                oppoUserBatchMapper.updateById(batch);
            } catch (Exception updateException) {
                logger.error("更新批次状态失败: {}", updateException.getMessage());
            }

            throw new RuntimeException("批量上传用户数据失败: " + e.getMessage(), e);
        } finally {
            LocalDateTime endTime = LocalDateTime.now();
            response.setEndTime(endTime);
            response.setProcessingTimeMs(java.time.Duration.between(startTime, endTime).toMillis());
        }
        
        return response;
    }

    /**
     * 通过 Excel 文件批量上传用户数据
     * @param file Excel 文件
     * @param token 认证令牌
     * @param overwrite 是否覆盖已存在的数据
     * @param batchSize 批次大小
     * @return 上传响应
     */
    public OppoUserUploadResponse uploadUsersFromExcel(MultipartFile file, String token, Boolean overwrite, Integer batchSize) {
        LocalDateTime startTime = LocalDateTime.now();
        OppoUserUploadResponse response = new OppoUserUploadResponse();
        response.setStartTime(startTime);

        // 生成批次ID和批次名称
        String batchId = BatchIdGenerator.generateExcelBatchId();
        String batchName = BatchIdGenerator.generateBatchName("Excel", file.getOriginalFilename());

        // 创建批次记录
        OppoUserBatch batch = new OppoUserBatch(batchId, batchName, "EXCEL");
        batch.setFileName(file.getOriginalFilename());
        batch.setFileSize(file.getSize());
        batch.setCreatedBy("SYSTEM");

        // 设置批次信息到响应中
        response.setBatchId(batchId);
        response.setBatchName(batchName);
        response.setUploadSource("EXCEL");

        try {
            // 保存批次记录
            oppoUserBatchMapper.insert(batch);
            logger.info("创建批次记录: {}", batchId);

            // 校验Token
            if (tokenValidationService != null) {
                tokenValidationService.validateTokenOrThrow(token);
            } else {
                logger.warn("TokenValidationService 未注入，跳过Token校验");
            }

            // 文件格式校验
            if (!ExcelUtil.isValidExcelFile(file)) {
                batch.setFailed("文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件");
                try {
                    oppoUserBatchMapper.updateById(batch);
                } catch (Exception e) {
                    logger.error("更新批次状态失败: {}", e.getMessage());
                }
                // 即使失败也要返回批次信息
                response.setFailedCount(1);
                response.setSuccessCount(0);
                throw new RuntimeException("文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件");
            }

            logger.info("开始处理 Excel 文件: {}, 大小: {} bytes, 批次ID: {}",
                       file.getOriginalFilename(), file.getSize(), batchId);

            // 从 Excel 文件中读取用户数据
            List<OppoUserUploadRequest.OppoUserData> userData = ExcelUtil.readUsersFromExcel(file);

            if (userData.isEmpty()) {
                batch.setFailed("Excel 文件中没有找到有效的用户数据");
                try {
                    oppoUserBatchMapper.updateById(batch);
                } catch (Exception e) {
                    logger.error("更新批次状态失败: {}", e.getMessage());
                }
                // 即使失败也要返回批次信息
                response.setTotalCount(0);
                response.setFailedCount(0);
                response.setSuccessCount(0);
                throw new RuntimeException("Excel 文件中没有找到有效的用户数据");
            }

            // 获取当前周表名
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            response.setTableName(currentWeekTable);

            // 确保当前周表存在
            ensureWeeklyTableExists(currentWeekTable);

            // 转换为实体对象，设置批次信息
            List<OppoUser> userList = convertToOppoUsersWithBatch(userData, batchId, batchName, "EXCEL");
            response.setTotalCount(userList.size());

            // 更新批次总数
            batch.setTotalCount(userList.size());
            oppoUserBatchMapper.updateById(batch);

            logger.info("开始批量上传用户数据到表 {}，共 {} 条记录，批次ID: {}",
                       currentWeekTable, userList.size(), batchId);

            // 分批处理
            int actualBatchSize = batchSize != null ? batchSize : 100;
            // 设置默认的 overwrite 行为：默认为 false，避免重复数据
            boolean isOverwrite = overwrite != null ? overwrite : false;
            int successCount = 0;
            int failedCount = 0;
            List<OppoUserUploadResponse.FailedRecord> failedRecords = new ArrayList<>();

            logger.info("开始分批处理用户数据，总数: {}, 批次大小: {}, 去重模式: {} (overwrite={})",
                       userList.size(), actualBatchSize, !isOverwrite ? "启用" : "禁用", isOverwrite);

            for (int i = 0; i < userList.size(); i += actualBatchSize) {
                int endIndex = Math.min(i + actualBatchSize, userList.size());
                List<OppoUser> batchUsers = userList.subList(i, endIndex);

                try {
                    logger.info("处理第 {} 批用户数据，共 {} 条，批次ID: {}, 去重模式: {}",
                                (i / actualBatchSize) + 1, batchUsers.size(), batchId, !isOverwrite ? "启用" : "禁用");

                    // 确保所有记录都有正确的时间戳
                    LocalDateTime batchTime = LocalDateTime.now();
                    for (OppoUser user : batchUsers) {
                        if (user.getCreateTime() == null) {
                            user.setCreateTime(batchTime);
                        }
                        // 总是更新 update_time 为当前时间
                        user.setUpdateTime(batchTime);
                    }

                    // 记录一些样本数据用于调试
                    if (logger.isDebugEnabled() && !batchUsers.isEmpty()) {
                        OppoUser sample = batchUsers.get(0);
                        logger.debug("样本数据 - douyinId: {}, starMapId: {}, batchId: {}, updateTime: {}",
                                    sample.getDouyinId(), sample.getStarMapId(), sample.getBatchId(), sample.getUpdateTime());
                    }

                    int insertCount;
                    if (isOverwrite) {
                        // 覆盖模式：使用 ON DUPLICATE KEY UPDATE（更新已存在的记录）
                        logger.debug("使用覆盖模式插入到表: {} (允许更新重复数据)", currentWeekTable);
                        insertCount = oppoUserMapper.batchInsertToTable(currentWeekTable, batchUsers);
                        int originalCount = insertCount;
                        insertCount = Math.min(insertCount, batchUsers.size());
                        logger.debug("覆盖模式插入结果 - 原始返回值: {}, 修正后: {}", originalCount, insertCount);
                    } else {
                        // 去重模式：使用 INSERT IGNORE（重复数据会被忽略，只保留一条）
                        logger.debug("使用去重模式插入到表: {} (忽略重复数据)", currentWeekTable);
                        insertCount = oppoUserMapper.batchInsertOnlyToTable(currentWeekTable, batchUsers);
                        logger.debug("去重模式插入结果: {} (重复数据已忽略)", insertCount);
                    }

                    successCount += insertCount;
                    logger.info("第 {} 批用户数据处理完成，实际处理 {} 条（覆盖模式: {}），累计成功: {}",
                                (i / actualBatchSize) + 1, insertCount, isOverwrite, successCount);

                } catch (Exception e) {
                    logger.error("第 {} 批用户数据处理失败: {}", (i / actualBatchSize) + 1, e.getMessage(), e);
                    failedCount += batchUsers.size();

                    // 记录失败详情
                    for (OppoUser user : batchUsers) {
                        failedRecords.add(new OppoUserUploadResponse.FailedRecord(
                            user.getDouyinId(), user.getStarMapId(), e.getMessage()));
                    }
                }
            }

            response.setSuccessCount(successCount);
            response.setFailedCount(failedCount);
            response.setFailedRecords(failedRecords);

            // 更新批次统计信息
            batch.updateCounts(userList.size(), successCount, failedCount);
            oppoUserBatchMapper.updateById(batch);

            logger.info("Excel 文件处理完成: {}, 批次ID: {}, 总计: {}, 成功: {}, 失败: {}",
                       file.getOriginalFilename(), batchId, response.getTotalCount(),
                       successCount, failedCount);

        } catch (Exception e) {
            logger.error("Excel 文件上传失败: {}", e.getMessage(), e);

            // 更新批次为失败状态
            if (batch.getId() != null) {
                batch.setFailed(e.getMessage());
                oppoUserBatchMapper.updateById(batch);
            }

            response.setFailedCount(response.getTotalCount() != null ? response.getTotalCount() : 0);
            response.setSuccessCount(0);
            throw new RuntimeException("Excel 文件上传失败: " + e.getMessage(), e);
        } finally {
            LocalDateTime endTime = LocalDateTime.now();
            response.setEndTime(endTime);
            response.setProcessingTimeMs(java.time.Duration.between(startTime, endTime).toMillis());
        }

        return response;
    }
    
    /**
     * 根据抖音ID查询用户
     * @param douyinId 抖音ID
     * @return 用户对象
     */
    public OppoUser getUserByDouyinId(String douyinId) {
        try {
            if (!StringUtils.hasText(douyinId)) {
                logger.warn("抖音ID为空，无法查询");
                return null;
            }
            
            // 查询最近12周的数据
            List<String> tableNames = UserWeeklyTableUtil.getRecentWeekTableNames(12);
            
            for (String tableName : tableNames) {
                try {
                    if (oppoUserMapper.checkTableExists(tableName) > 0) {
                        OppoUser user = oppoUserMapper.selectByDouyinIdFromTable(tableName, douyinId);
                        if (user != null) {
                            logger.debug("从表 {} 查询到用户: {}", tableName, douyinId);
                            return user;
                        }
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }
            
            logger.debug("未找到抖音ID为 {} 的用户", douyinId);
            return null;
            
        } catch (Exception e) {
            logger.error("根据抖音ID查询用户失败: douyinId={}, error={}", douyinId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 根据星图ID查询用户
     * @param starMapId 星图ID
     * @return 用户对象
     */
    public OppoUser getUserByStarMapId(String starMapId) {
        try {
            if (!StringUtils.hasText(starMapId)) {
                logger.warn("星图ID为空，无法查询");
                return null;
            }
            
            // 查询最近12周的数据
            List<String> tableNames = UserWeeklyTableUtil.getRecentWeekTableNames(12);
            
            for (String tableName : tableNames) {
                try {
                    if (oppoUserMapper.checkTableExists(tableName) > 0) {
                        OppoUser user = oppoUserMapper.selectByStarMapIdFromTable(tableName, starMapId);
                        if (user != null) {
                            logger.debug("从表 {} 查询到用户: {}", tableName, starMapId);
                            return user;
                        }
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }
            
            logger.debug("未找到星图ID为 {} 的用户", starMapId);
            return null;
            
        } catch (Exception e) {
            logger.error("根据星图ID查询用户失败: starMapId={}, error={}", starMapId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 转换上传数据为实体对象
     * @param userData 上传数据列表
     * @return 实体对象列表
     */
    private List<OppoUser> convertToOppoUsers(List<OppoUserUploadRequest.OppoUserData> userData) {
        List<OppoUser> userList = new ArrayList<>();

        for (OppoUserUploadRequest.OppoUserData data : userData) {
            if (StringUtils.hasText(data.getDouyinId()) || StringUtils.hasText(data.getStarMapId())) {
                OppoUser user = new OppoUser();
                user.setDouyinId(data.getDouyinId());
                user.setStarMapId(data.getStarMapId());
                user.setDeleted(0);
                user.setUploadSource("MANUAL");
                userList.add(user);
            }
        }

        return userList;
    }

    
    /**
     * 转换上传数据为实体对象（带批次信息）
     * @param userData 上传数据列表
     * @param batchId 批次ID
     * @param batchName 批次名称
     * @param uploadSource 上传来源
     * @return 实体对象列表
     */
    private List<OppoUser> convertToOppoUsersWithBatch(List<OppoUserUploadRequest.OppoUserData> userData,
                                                      String batchId, String batchName, String uploadSource) {
        List<OppoUser> userList = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();

        for (OppoUserUploadRequest.OppoUserData data : userData) {
            if (StringUtils.hasText(data.getDouyinId()) || StringUtils.hasText(data.getStarMapId())) {
                OppoUser user = new OppoUser();
                user.setDouyinId(data.getDouyinId());
                user.setStarMapId(data.getStarMapId());
                user.setDouyinLink(data.getDouyinLink());  // 新增抖音链接字段
                user.setBatchId(batchId);
                user.setBatchName(batchName);
                user.setUploadSource(uploadSource);
                user.setDeleted(0);
                user.setCreateTime(now);
                user.setUpdateTime(now);
                userList.add(user);
            }
        }

        return userList;
    }

    /**
     * 根据批次ID查询用户列表
     * @param batchId 批次ID
     * @return 用户列表
     */
    public List<OppoUser> getUsersByBatchId(String batchId) {
        try {
            if (!StringUtils.hasText(batchId)) {
                logger.warn("批次ID为空，无法查询");
                return new ArrayList<>();
            }

            List<OppoUser> allUsers = new ArrayList<>();

            // 查询最近12周的数据
            List<String> tableNames = UserWeeklyTableUtil.getRecentWeekTableNames(12);

            for (String tableName : tableNames) {
                try {
                    if (oppoUserMapper.checkTableExists(tableName) > 0) {
                        List<OppoUser> users = oppoUserMapper.selectByBatchIdFromTable(tableName, batchId);
                        if (users != null && !users.isEmpty()) {
                            allUsers.addAll(users);
                            logger.debug("从表 {} 查询到批次 {} 的 {} 个用户", tableName, batchId, users.size());
                        }
                    }
                } catch (Exception e) {
                    logger.warn("查询表 {} 失败: {}", tableName, e.getMessage());
                }
            }

            logger.debug("批次 {} 共查询到 {} 个用户", batchId, allUsers.size());
            return allUsers;

        } catch (Exception e) {
            logger.error("根据批次ID查询用户失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 确保周表存在，如果不存在则创建
     * @param tableName 表名
     */
    private void ensureWeeklyTableExists(String tableName) {
        try {
            if (oppoUserMapper.checkTableExists(tableName) == 0) {
                logger.info("周表 {} 不存在，开始创建", tableName);

                // 使用第一个周的表作为模板
                String templateTable = "oppo_user_2025_01";
                oppoUserMapper.createWeeklyTable(tableName, templateTable);

                logger.info("周表 {} 创建成功", tableName);
            } else {
                logger.debug("周表 {} 已存在", tableName);
            }
        } catch (Exception e) {
            logger.error("创建周表 {} 失败: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("创建周表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 表数量
     */
    public int checkTableExists(String tableName) {
        return oppoUserMapper.checkTableExists(tableName);
    }

    /**
     * 根据批次ID获取用户ID列表
     * @param batchId 批次ID，如果为空则获取最近的批次
     * @return 用户ID列表（抖音ID和星图ID）
     */
    public List<String> getUserIdsByBatchId(String batchId) {
        try {
            // 如果没有指定批次ID，获取最近的成功批次
            if (!StringUtils.hasText(batchId)) {
                OppoUserBatch latestBatch = oppoUserBatchMapper.selectLatestSuccessBatch();
                if (latestBatch == null) {
                    logger.warn("没有找到任何成功的批次");
                    return new ArrayList<>();
                }
                batchId = latestBatch.getBatchId();
                logger.info("使用最近的批次: {}", batchId);
            }

            // 根据批次ID获取用户列表
            List<OppoUser> users = getUsersByBatchId(batchId);

            if (users.isEmpty()) {
                logger.warn("批次 {} 中没有找到用户数据", batchId);
                return new ArrayList<>();
            }

            // 提取所有的用户ID（抖音ID和星图ID）
            List<String> userIds = new ArrayList<>();
            for (OppoUser user : users) {
                if (StringUtils.hasText(user.getDouyinId())) {
                    userIds.add(user.getDouyinId());
                }
                if (StringUtils.hasText(user.getStarMapId())) {
                    userIds.add(user.getStarMapId());
                }
            }

            // 去重
            userIds = userIds.stream().distinct().collect(java.util.stream.Collectors.toList());

            logger.info("批次 {} 共提取到 {} 个唯一用户ID", batchId, userIds.size());
            return userIds;

        } catch (Exception e) {
            logger.error("根据批次ID获取用户ID失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取最近的成功批次信息
     * @return 最近的成功批次
     */
    public OppoUserBatch getLatestSuccessBatch() {
        try {
            return oppoUserBatchMapper.selectLatestSuccessBatch();
        } catch (Exception e) {
            logger.error("获取最近的成功批次失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据批次ID获取批次信息
     * @param batchId 批次ID
     * @return 批次信息
     */
    public OppoUserBatch getBatchById(String batchId) {
        try {
            return oppoUserBatchMapper.selectByBatchId(batchId);
        } catch (Exception e) {
            logger.error("根据批次ID获取批次信息失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 统计指定表中已存在的抖音ID数量
     * @param tableName 表名
     * @param douyinIds 抖音ID列表
     * @return 已存在的数量
     */
    public int countExistingByDouyinIds(String tableName, List<String> douyinIds) {
        try {
            if (douyinIds == null || douyinIds.isEmpty()) {
                return 0;
            }
            return oppoUserMapper.countExistingByDouyinIds(tableName, douyinIds);
        } catch (Exception e) {
            logger.error("统计已存在的抖音ID失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计指定表中已存在的星图ID数量
     * @param tableName 表名
     * @param starMapIds 星图ID列表
     * @return 已存在的数量
     */
    public int countExistingByStarMapIds(String tableName, List<String> starMapIds) {
        try {
            if (starMapIds == null || starMapIds.isEmpty()) {
                return 0;
            }
            return oppoUserMapper.countExistingByStarMapIds(tableName, starMapIds);
        } catch (Exception e) {
            logger.error("统计已存在的星图ID失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计指定表中指定批次的记录数量
     * @param tableName 表名
     * @param batchId 批次ID
     * @return 记录数量
     */
    public int countByBatchId(String tableName, String batchId) {
        try {
            if (!StringUtils.hasText(batchId)) {
                return 0;
            }
            return oppoUserMapper.countByBatchId(tableName, batchId);
        } catch (Exception e) {
            logger.error("统计批次记录数失败: tableName={}, batchId={}, error={}", tableName, batchId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 根据抖音ID列表查询用户记录
     * @param tableName 表名
     * @param douyinIds 抖音ID列表
     * @return 用户记录列表
     */
    public List<OppoUser> findByDouyinIds(String tableName, List<String> douyinIds) {
        try {
            if (douyinIds == null || douyinIds.isEmpty()) {
                return new ArrayList<>();
            }
            return oppoUserMapper.selectByDouyinIds(tableName, douyinIds);
        } catch (Exception e) {
            logger.error("根据抖音ID查询用户失败: tableName={}, douyinIds={}, error={}", tableName, douyinIds, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    public boolean checkTableExists(String tableName) {
        try {
            return oppoUserMapper.checkTableExists(tableName) > 0;
        } catch (Exception e) {
            logger.error("检查表 {} 是否存在失败: {}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 统计指定表中指定批次的用户数量
     * @param tableName 表名
     * @param batchId 批次ID
     * @return 用户数量
     */
    public int countUsersByBatchId(String tableName, String batchId) {
        try {
            return oppoUserMapper.countByBatchId(tableName, batchId);
        } catch (Exception e) {
            logger.error("统计表 {} 中批次 {} 的用户数量失败: {}", tableName, batchId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 根据抖音ID查询所有匹配的用户（用于检测重复）
     * @param tableName 表名
     * @param douyinId 抖音ID
     * @return 用户列表
     */
    public List<OppoUser> findByDouyinId(String tableName, String douyinId) {
        try {
            return oppoUserMapper.selectAllByDouyinId(tableName, douyinId);
        } catch (Exception e) {
            logger.error("根据抖音ID查询所有用户失败: tableName={}, douyinId={}, error={}", tableName, douyinId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取当前周表名
     * @return 当前周表名
     */
    public String getCurrentWeekTableName() {
        return UserWeeklyTableUtil.getCurrentWeekTableName();
    }

    /**
     * 获取表中总记录数
     * @param tableName 表名
     * @return 总记录数
     */
    public int getTotalRecordCount(String tableName) {
        try {
            return oppoUserMapper.getTotalRecordCount(tableName);
        } catch (Exception e) {
            logger.error("获取表记录总数失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取唯一抖音ID数量
     * @param tableName 表名
     * @return 唯一抖音ID数量
     */
    public int getUniqueDouyinIdCount(String tableName) {
        try {
            return oppoUserMapper.getUniqueDouyinIdCount(tableName);
        } catch (Exception e) {
            logger.error("获取唯一抖音ID数量失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取唯一星图ID数量
     * @param tableName 表名
     * @return 唯一星图ID数量
     */
    public int getUniqueStarMapIdCount(String tableName) {
        try {
            return oppoUserMapper.getUniqueStarMapIdCount(tableName);
        } catch (Exception e) {
            logger.error("获取唯一星图ID数量失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return 0;
        }
    }
}
