package data.oppodataapi.controller;

import data.oppodataapi.api.QuarkApi;
import data.oppodataapi.dto.ApiResponse;
import data.oppodataapi.dto.DocSourceRequest;
import data.oppodataapi.dto.QueryRequest;
import data.oppodataapi.dto.UserQueryRequest;
import data.oppodataapi.dto.BatchQueryRequest;
import data.oppodataapi.dto.PageQueryRequest;
import data.oppodataapi.dto.PageResponse;
import data.oppodataapi.entity.OppoData;
import data.oppodataapi.entity.OppoUserBatch;
import data.oppodataapi.service.OppoDataService;
import data.oppodataapi.service.OppoUserService;
import data.oppodataapi.service.TokenValidationService;
import data.oppodataapi.util.WeeklyTableUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OPPO数据API控制器
 */
@RestController
@RequestMapping("/api/oppo")
@CrossOrigin(origins = "*")
public class OppoDataController {

    private static final Logger logger = LoggerFactory.getLogger(OppoDataController.class);

    @Autowired
    private OppoDataService oppoDataService;

    @Autowired
    private OppoUserService oppoUserService;

    @Autowired
    private TokenValidationService tokenValidationService;

    /**
     * 查询数据中心数据
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/query")
    public ApiResponse<Object> queryDataCenter(@RequestBody QueryRequest request) {
        try {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.info("接收到数据中心查询请求: {}", request);
            logger.info("接收到数据中心查询请求");

            Object result = oppoDataService.queryDataCenter(request);

            logger.info("数据中心查询成功");
            return ApiResponse.success(result, "查询成功");

        } catch (Exception e) {
            logger.error("数据中心查询失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询文档源数据
     * @param request 文档源查询请求
     * @return 查询结果
     */
    @PostMapping("/doc-source")
    public ApiResponse<Object> queryDocSource(@RequestBody DocSourceRequest request) {
        try {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.info("接收到文档源查询请求: {}", request);
            logger.info("接收到文档源查询请求");

            Object result = oppoDataService.queryDocSource(request);

            logger.info("文档源查询成功");
            return ApiResponse.success(result, "查询成功");

        } catch (Exception e) {
            logger.error("文档源查询失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     * @return 健康状态
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("OK", "服务正常运行");
    }

    /**
     * 根据用户ID查询数据
     * @param request 用户查询请求
     * @return 查询结果
     */
    @PostMapping("/query-by-userids")
    public ApiResponse<List<OppoData>> queryByUserIds(@RequestBody UserQueryRequest request) {
        try {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.info("接收到用户ID查询请求: {}", request);
            logger.info("接收到用户ID查询请求");

            String userIds = request.getUserIds();
            if (userIds == null || userIds.isEmpty()) {
                return ApiResponse.error("用户ID不能为空");
            }

            // 创建DocSourceRequest对象
            DocSourceRequest docSourceRequest = new DocSourceRequest();

            // 设置extraCondition - 确保字段顺序与正常参数一致
            List<Object> extraCondition = new ArrayList<>();
            Map<String, Object> condition = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
            condition.put("fieldId", 446);
            condition.put("operator", 0);

            // 直接使用逗号分隔的字符串，不添加引号
            List<String> userIdList = Arrays.stream(userIds.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());

            condition.put("value", userIdList);
            extraCondition.add(condition);
            docSourceRequest.setExtraCondition(extraCondition);
            docSourceRequest.setMainPartyId(Collections.singletonList(341497));

            // 设置其他必要参数 - 确保与正常参数一致
            docSourceRequest.setIndexType("user");
            docSourceRequest.setPath("oppo");
            docSourceRequest.setPageNum(request.getPageNum() != null ? request.getPageNum() : 1);
            docSourceRequest.setPageSize(request.getPageSize() != null ? request.getPageSize() : 1000);
            docSourceRequest.setFlatSource(true);
            docSourceRequest.setPlatformIds(Collections.singletonList(44));
            docSourceRequest.setHighLight(true);
            docSourceRequest.setArrayFlatSource(true);
            docSourceRequest.setTop(10); // 使用10而不是100
            docSourceRequest.setTitle("总计");
            docSourceRequest.setColumnVo(null);
            docSourceRequest.setRowVo(new ArrayList<>()); // 使用空列表而不是null
            docSourceRequest.setMetricsVo(new ArrayList<>()); // 使用空列表而不是null
            docSourceRequest.setToken(request.getToken());

            // 设置downloadCount
            docSourceRequest.setDownloadCount(Collections.singletonList("0"));

            // 调用服务
            Object result = oppoDataService.queryDocSource(docSourceRequest);

            // 转换结果为List<OppoData>
            List<OppoData> oppoDataList = oppoDataService.convertToOppoDataList(result);

            if (!oppoDataList.isEmpty()) {
              oppoDataService.saveAll(oppoDataList);
            }


            logger.info("用户ID查询成功，返回记录数: {}", oppoDataList.size());
            return ApiResponse.success(oppoDataList, "查询成功");

        } catch(Exception e){
            logger.error("用户ID查询失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }




    /**
     * 根据用户ID从数据库查询数据
     * @param userId 用户ID
     * @return 查询结果
     */
    @GetMapping("/db/user/{userId}")
    public ApiResponse<List<OppoData>> getDataByUserId(@PathVariable String userId) {
        try {
            logger.info("接收到数据库用户ID查询请求: userId={}", userId);
            List<OppoData> data = oppoDataService.getDataByUserId(userId);
            return ApiResponse.success(data, "查询成功");
        } catch (Exception e) {
            logger.error("数据库用户ID查询失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据星图ID从数据库查询数据
     * @param starMapId 星图ID
     * @return 查询结果
     */
    @GetMapping("/db/starmap/{starMapId}")
    public ApiResponse<OppoData> getDataByStarMapId(@PathVariable String starMapId) {
        try {
            logger.info("接收到数据库星图ID查询请求: starMapId={}", starMapId);
            OppoData data = oppoDataService.getDataByStarMapId(starMapId);
            return ApiResponse.success(data, "查询成功");
        } catch (Exception e) {
            logger.error("数据库星图ID查询失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有数据
     * @return 查询结果
     */
    @GetMapping("/db/all")
    public ApiResponse<List<OppoData>> getAllData() {
        try {
            logger.info("接收到查询所有数据的请求");
            List<OppoData> data = oppoDataService.getAllData();
            return ApiResponse.success(data, "查询成功");
        } catch (Exception e) {
            logger.error("查询所有数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询OppoData数据（优化版，支持多条件查询）
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    @PostMapping("/page")
    public ApiResponse<PageResponse<OppoData>> queryOppoDataByPage(@RequestBody PageQueryRequest request) {
        try {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.info("接收到优化的分页查询请求: {}", request);
            logger.info("接收到优化的分页查询请求");

            // Token验证
            if (tokenValidationService != null) {
                try {
                    tokenValidationService.validateTokenOrThrow(request.getToken());
                } catch (RuntimeException e) {
                    logger.error("Token验证失败: {}", e.getMessage());
                    return ApiResponse.error("Token验证失败: " + e.getMessage(), "401");
                }
            } else {
                logger.warn("TokenValidationService 未注入，跳过Token校验");
            }

            // 参数验证和标准化
            try {
                request.validate();
            } catch (IllegalArgumentException e) {
                logger.error("参数验证失败: {}", e.getMessage());
                return ApiResponse.error("参数验证失败: " + e.getMessage(), "400");
            }

            // 确定要查询的周表名
            String tableName = request.getWeekTableName();
            if (!StringUtils.hasText(tableName)) {
                // 查找最新的有数据的周表
                tableName = oppoDataService.findLatestTableWithData();
                logger.info("未指定周表，自动查找到最新有数据的周表: {}", tableName);
            } else {
                logger.info("使用指定的周表: {}", tableName);
            }

            // 检查表是否存在
            if (!oppoDataService.checkTableExists(tableName)) {
                logger.warn("表 {} 不存在", tableName);
                PageResponse<OppoData> emptyResult = PageResponse.of(request.getPageNum(), request.getPageSize(), 0L, new ArrayList<>());
                return ApiResponse.success(emptyResult, "指定的表不存在，返回空结果", "404");
            }

            // 执行优化的分页查询
            PageResponse<OppoData> result = oppoDataService.queryOppoDataByPageOptimized(request, tableName);

            // 构建响应消息
            String message = String.format("查询成功，共找到 %d 条数据", result.getTotal());
            if (StringUtils.hasText(request.getBatchId())) {
                message += "，批次: " + request.getBatchId();
            }
            if (StringUtils.hasText(tableName)) {
                message += "，表: " + tableName;
            }

            logger.info("优化分页查询成功，返回 {} 条记录，总计 {} 条，表: {}",
                       result.getData().size(), result.getTotal(), tableName);

            // 根据查询结果设置不同的状态码
            if (result.getTotal() == 0) {
                return ApiResponse.success(result, "查询成功，但未找到匹配的数据", "204");
            } else {
                return ApiResponse.success(result, message, "200");
            }

        } catch (RuntimeException e) {
            // Token校验失败或其他业务异常
            logger.error("分页查询业务异常: {}", e.getMessage());
            if (e.getMessage().contains("Token") || e.getMessage().contains("认证")) {
                return ApiResponse.error("认证失败: " + e.getMessage(), "401");
            } else if (e.getMessage().contains("权限")) {
                return ApiResponse.error("权限不足: " + e.getMessage(), "403");
            } else {
                return ApiResponse.error("业务异常: " + e.getMessage(), "400");
            }
        } catch (Exception e) {
            // 注释掉详细的请求参数打印，避免泄露业务数据
            // logger.error("优化分页查询系统异常: request={}, error={}", request, e.getMessage(), e);
            logger.error("优化分页查询系统异常: error={}", e.getMessage(), e);
            return ApiResponse.error("系统异常，请稍后重试: " + e.getMessage(), "500");
        }
    }

    /**
     * 根据批次ID查询数据
     * @param request 批次查询请求
     * @return 查询结果
     */
    @PostMapping("/query-by-batch")
    public ApiResponse<PageResponse<OppoData>> queryByBatch(@RequestBody BatchQueryRequest request) {
        try {
            logger.info("接收到批次查询请求: batchId={}, pageNum={}, pageSize={}",
                       request.getBatchId(), request.getPageNum(), request.getPageSize());

            // 参数验证
            if (request.getPageNum() == null || request.getPageNum() < 1) {
                request.setPageNum(1);
            }
            if (request.getPageSize() == null || request.getPageSize() < 1) {
                request.setPageSize(10);
            }
            if (request.getPageSize() > 1000) {
                request.setPageSize(1000); // 限制最大页面大小
            }

            // 根据批次ID获取用户ID列表
            List<String> userIds = oppoUserService.getUserIdsByBatchId(request.getBatchId());

            if (userIds.isEmpty()) {
                // 如果没有找到用户ID，返回空结果
                PageResponse<OppoData> emptyResponse = new PageResponse<>();
                emptyResponse.setPageNum(request.getPageNum());
                emptyResponse.setPageSize(request.getPageSize());
                emptyResponse.setData(new ArrayList<>());

                String message = StringUtils.hasText(request.getBatchId()) ?
                    "批次 " + request.getBatchId() + " 中没有找到用户数据" :
                    "没有找到任何批次数据";

                return ApiResponse.success(emptyResponse, message);
            }

            // 确定要查询的周表名
            String weekTableName = request.getWeekTableName();
            if (!StringUtils.hasText(weekTableName)) {
                // 查找最新的有数据的周表
                weekTableName = oppoDataService.findLatestTableWithData();
                logger.info("未指定周表，自动查找到最新有数据的周表: {}", weekTableName);
            } else {
                logger.info("使用指定的周表: {}", weekTableName);
            }

            // 直接查询指定的周表数据
            logger.info("开始从周表 {} 查询批次数据，用户ID数量: {}", weekTableName, userIds.size());
            PageResponse<OppoData> result = oppoDataService.queryByUserIdsFromWeekTable(
                userIds, weekTableName, request.getPageNum(), request.getPageSize());

            // 添加批次信息到响应中
            if (result != null) {
                // 获取批次信息
                String actualBatchId = StringUtils.hasText(request.getBatchId()) ?
                    request.getBatchId() :
                    (oppoUserService.getLatestSuccessBatch() != null ?
                     oppoUserService.getLatestSuccessBatch().getBatchId() : "未知");

                logger.info("批次查询完成: batchId={}, 周表={}, 查询到{}条数据",
                           actualBatchId, weekTableName, result.getTotal());

                return ApiResponse.success(result,
                    String.format("批次 %s 从周表 %s 查询成功，共找到 %d 条数据", actualBatchId, weekTableName, result.getTotal()));
            } else {
                return ApiResponse.error("查询失败，请检查参数");
            }

        } catch (Exception e) {
            logger.error("批次查询失败: batchId={}, error={}", request.getBatchId(), e.getMessage(), e);
            return ApiResponse.error("批次查询失败: " + e.getMessage());
        }
    }


    /**
     * 根据批次ID查询数据并跑数
     * @param
     * @return 查询结果
     */
    @GetMapping("/run-data-by-batch")
    public ApiResponse<List<OppoData>> runDataByBatch(@RequestParam(value = "batch_id",required = true ) String batchId) {
        try {

            // 根据批次ID获取用户ID列表
            List<String> userIds = oppoUserService.getUserIdsByBatchId(batchId);

            List<OppoData> queryQuarkData = oppoDataService.createQueryQuarkData(userIds);
            if (queryQuarkData.isEmpty()) {
                return ApiResponse.error("没有查询到数据");
            }

            return ApiResponse.success(queryQuarkData, "获取批次信息成功");
        } catch (Exception e) {
            logger.error("批次跑数失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return ApiResponse.error("批次跑数失败: " + e.getMessage());
        }
    }

    /**
     * 获取批次查询信息
     * @param batchId 批次ID（可选）
     * @return 批次信息
     */
    @GetMapping("/batch-info")
    public ApiResponse<Map<String, Object>> getBatchInfo(@RequestParam(required = false) String batchId) {
        try {
            Map<String, Object> info = new HashMap<>();

            OppoUserBatch batch;
            if (StringUtils.hasText(batchId)) {
                // 查询指定批次
                // 通过OppoUserService获取批次信息
                batch = oppoUserService.getBatchById(batchId);
                if (batch == null) {
                    return ApiResponse.error("批次 " + batchId + " 不存在");
                }
            } else {
                // 查询最近的批次
                batch = oppoUserService.getLatestSuccessBatch();
                if (batch == null) {
                    return ApiResponse.error("没有找到任何成功的批次");
                }
            }

            // 获取该批次的用户ID数量
            List<String> userIds = oppoUserService.getUserIdsByBatchId(batch.getBatchId());

            info.put("batch", batch);
            info.put("userIdCount", userIds.size());
            info.put("userIds", userIds.size() <= 100 ? userIds : userIds.subList(0, 100)); // 最多显示100个ID
            info.put("isLatest", !StringUtils.hasText(batchId));

            return ApiResponse.success(info, "获取批次信息成功");

        } catch (Exception e) {
            logger.error("获取批次信息失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return ApiResponse.error("获取批次信息失败: " + e.getMessage());
        }
    }

    /**
     * 诊断批次数据处理问题
     * @param batchId 批次ID
     * @return 诊断结果
     */
    @GetMapping("/diagnose-batch-data")
    public ApiResponse<Map<String, Object>> diagnoseBatchData(@RequestParam(required = false) String batchId) {
        try {
            Map<String, Object> diagnosis = new HashMap<>();

            // 获取批次信息
            OppoUserBatch batch;
            if (StringUtils.hasText(batchId)) {
                batch = oppoUserService.getBatchById(batchId);
                if (batch == null) {
                    return ApiResponse.error("批次 " + batchId + " 不存在");
                }
            } else {
                batch = oppoUserService.getLatestSuccessBatch();
                if (batch == null) {
                    return ApiResponse.error("没有找到任何成功的批次");
                }
            }

            // 获取用户ID列表
            List<String> userIds = oppoUserService.getUserIdsByBatchId(batch.getBatchId());

            // 检查当前周表中的数据
            String currentWeekTable = WeeklyTableUtil.getCurrentWeekTableName();
            PageResponse<OppoData> currentWeekData = oppoDataService.queryByUserIdsFromWeekTable(
                userIds, currentWeekTable, 1, 10);

            // 统计信息
            diagnosis.put("batch", batch);
            diagnosis.put("userIdCount", userIds.size());
            diagnosis.put("currentWeekTable", currentWeekTable);
            diagnosis.put("currentWeekDataCount", currentWeekData.getTotal());
            diagnosis.put("sampleUserIds", userIds.size() > 10 ? userIds.subList(0, 10) : userIds);

            // 检查数据分布
            Map<String, Object> dataDistribution = new HashMap<>();
            dataDistribution.put("expectedBatches", Math.ceil((double) userIds.size() / 100));
            dataDistribution.put("actualDataCount", currentWeekData.getTotal());
            dataDistribution.put("dataPercentage", userIds.size() > 0 ?
                (double) currentWeekData.getTotal() / userIds.size() * 100 : 0);

            diagnosis.put("dataDistribution", dataDistribution);

            // 问题分析
            List<String> issues = new ArrayList<>();
            if (currentWeekData.getTotal() == 0) {
                issues.add("当前周表中没有找到任何数据");
            } else if (currentWeekData.getTotal() < userIds.size()) {
                issues.add("数据不完整，可能存在分页查询问题");
            }

            if (currentWeekData.getTotal() == 100 && userIds.size() > 100) {
                issues.add("数据正好是100条，可能存在分页限制问题");
            }

            diagnosis.put("potentialIssues", issues);

            return ApiResponse.success(diagnosis, "诊断完成");

        } catch (Exception e) {
            logger.error("诊断批次数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新有数据的周表信息
     * @return 最新有数据的周表信息
     */
    @GetMapping("/latest-table")
    public ApiResponse<Map<String, Object>> getLatestTableWithData() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取最新有数据的表名
            String latestTable = oppoDataService.findLatestTableWithData();

            // 获取表的详细信息
            if (oppoDataService.checkTableExists(latestTable)) {
                int recordCount = oppoDataService.getTableRecordCount(latestTable);

                result.put("tableName", latestTable);
                result.put("recordCount", recordCount);
                result.put("exists", true);

                // 解析表名获取年周信息
                try {
                    String[] parts = latestTable.split("_");
                    if (parts.length >= 3) {
                        result.put("year", parts[2]);
                        result.put("week", parts[3]);
                    }
                } catch (Exception e) {
                    logger.warn("解析表名失败: {}", latestTable);
                }

            } else {
                result.put("tableName", latestTable);
                result.put("recordCount", 0);
                result.put("exists", false);
            }

            // 获取当前周表作为对比
            String currentTable = WeeklyTableUtil.getCurrentWeekTableName();
            result.put("currentWeekTable", currentTable);
            result.put("isCurrentWeek", latestTable.equals(currentTable));

            return ApiResponse.success(result, "获取最新有数据的周表成功");

        } catch (Exception e) {
            logger.error("获取最新有数据的周表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 清除最新表缓存
     * @return 操作结果
     */
    @PostMapping("/clear-table-cache")
    public ApiResponse<String> clearTableCache() {
        try {
            oppoDataService.clearLatestTableCache();
            return ApiResponse.success("缓存已清除", "最新表缓存清除成功");
        } catch (Exception e) {
            logger.error("清除缓存失败: {}", e.getMessage(), e);
            return ApiResponse.error("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 测试获取表记录数量方法
     * @param tableName 表名
     * @return 记录数量
     */
    @GetMapping("/test-table-count")
    public ApiResponse<Map<String, Object>> testTableCount(@RequestParam(required = false) String tableName) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 如果没有指定表名，使用最新有数据的表
            if (!StringUtils.hasText(tableName)) {
                tableName = oppoDataService.findLatestTableWithData();
            }

            // 检查表是否存在
            boolean exists = oppoDataService.checkTableExists(tableName);
            result.put("tableName", tableName);
            result.put("exists", exists);

            if (exists) {
                // 获取记录数量
                int recordCount = oppoDataService.getTableRecordCount(tableName);
                result.put("recordCount", recordCount);
                result.put("hasData", recordCount > 0);
            } else {
                result.put("recordCount", 0);
                result.put("hasData", false);
                result.put("error", "表不存在");
            }

            return ApiResponse.success(result, "获取表记录数量成功");

        } catch (Exception e) {
            logger.error("获取表记录数量失败: tableName={}, error={}", tableName, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试画像数据入库情况
     * @param userId 用户ID（可选）
     * @return 画像数据详情
     */
    @GetMapping("/test-portrait-data")
    public ApiResponse<Map<String, Object>> testPortraitData(@RequestParam(required = false) String userId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 确定要查询的表名
            String tableName = oppoDataService.findLatestTableWithData();
            result.put("tableName", tableName);

            // 构建查询条件
            String queryUserId = userId;
            if (!StringUtils.hasText(queryUserId)) {
                // 如果没有指定用户ID，查询第一个有画像数据的用户
                List<OppoData> dataList = oppoDataService.findUsersWithPortraitData(tableName, 1);
                if (!dataList.isEmpty()) {
                    queryUserId = dataList.get(0).getUserId();
                } else {
                    return ApiResponse.success(result, "未找到有画像数据的用户", "204");
                }
            }

            // 查询指定用户的画像数据
            OppoData userData = oppoDataService.findByUserId(tableName, queryUserId);
            if (userData == null) {
                return ApiResponse.error("未找到指定用户: " + queryUserId, "404");
            }

            result.put("userId", queryUserId);
            result.put("userNickname", userData.getUserNickname());

            // 检查原始画像数据
            Map<String, Object> portraitData = new HashMap<>();
            portraitData.put("fansDistributionRaw", userData.getObjectStarObjectFansDistribution());
            portraitData.put("viewersDistributionRaw", userData.getObjectStarObjectViewersDistribution());

            // 检查解析后的粉丝分布数据
            Map<String, Object> fansDistribution = new HashMap<>();
            fansDistribution.put("deviceDistribution", userData.getFansDeviceDistribution());
            fansDistribution.put("regionDistribution", userData.getFansRegionDistribution());
            fansDistribution.put("cityDistribution", userData.getFansCityDistribution());
            fansDistribution.put("genderDistribution", userData.getFansGenderDistribution());
            fansDistribution.put("ageDistribution", userData.getFansAgeDistribution());
            fansDistribution.put("cityLevel", userData.getFansCityLevel());
            fansDistribution.put("eightGroups", userData.getFansEightGroups());

            // 检查解析后的观众分布数据
            Map<String, Object> viewersDistribution = new HashMap<>();
            viewersDistribution.put("genderDistribution", userData.getViewersGenderDistribution());
            viewersDistribution.put("ageDistribution", userData.getViewersAgeDistribution());
            viewersDistribution.put("regionDistribution", userData.getViewersRegionDistribution());
            viewersDistribution.put("cityDistribution", userData.getViewersCityDistribution());
            viewersDistribution.put("cityLevelDistribution", userData.getViewersCityLevelDistribution());
            viewersDistribution.put("deviceDistribution", userData.getViewersDeviceDistribution());
            viewersDistribution.put("eightGroupsDistribution", userData.getViewersEightGroupsDistribution());

            result.put("portraitData", portraitData);
            result.put("fansDistribution", fansDistribution);
            result.put("viewersDistribution", viewersDistribution);

            // 统计哪些字段有数据
            Map<String, Boolean> dataStatus = new HashMap<>();
            dataStatus.put("hasFansRawData", userData.getObjectStarObjectFansDistribution() != null);
            dataStatus.put("hasViewersRawData", userData.getObjectStarObjectViewersDistribution() != null);
            dataStatus.put("hasFansDeviceDistribution", StringUtils.hasText(userData.getFansDeviceDistribution()));
            dataStatus.put("hasFansRegionDistribution", StringUtils.hasText(userData.getFansRegionDistribution()));
            dataStatus.put("hasFansCityDistribution", StringUtils.hasText(userData.getFansCityDistribution()));
            dataStatus.put("hasFansGenderDistribution", StringUtils.hasText(userData.getFansGenderDistribution()));
            dataStatus.put("hasFansAgeDistribution", StringUtils.hasText(userData.getFansAgeDistribution()));
            dataStatus.put("hasViewersGenderDistribution", StringUtils.hasText(userData.getViewersGenderDistribution()));
            dataStatus.put("hasViewersAgeDistribution", StringUtils.hasText(userData.getViewersAgeDistribution()));
            dataStatus.put("hasViewersRegionDistribution", StringUtils.hasText(userData.getViewersRegionDistribution()));

            result.put("dataStatus", dataStatus);

            return ApiResponse.success(result, "画像数据查询成功");

        } catch (Exception e) {
            logger.error("测试画像数据失败: userId={}, error={}", userId, e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试所有新增字段的入库情况
     * @param userId 用户ID（可选）
     * @return 新增字段数据详情
     */
    @GetMapping("/test-new-fields")
    public ApiResponse<Map<String, Object>> testNewFields(@RequestParam(required = false) String userId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 确定要查询的表名
            String tableName = oppoDataService.findLatestTableWithData();
            result.put("tableName", tableName);

            // 构建查询条件
            String queryUserId = userId;
            if (!StringUtils.hasText(queryUserId)) {
                // 如果没有指定用户ID，查询第一个用户
                List<OppoData> dataList = oppoDataService.findUsersWithPortraitData(tableName, 1);
                if (!dataList.isEmpty()) {
                    queryUserId = dataList.get(0).getUserId();
                } else {
                    return ApiResponse.success(result, "未找到用户数据", "204");
                }
            }

            // 查询指定用户的数据
            OppoData userData = oppoDataService.findByUserId(tableName, queryUserId);
            if (userData == null) {
                return ApiResponse.error("未找到指定用户: " + queryUserId, "404");
            }

            result.put("userId", queryUserId);
            result.put("userNickname", userData.getUserNickname());

            // 检查非商单相关字段（90天）
            Map<String, Object> nonCommercial90Days = new HashMap<>();
            nonCommercial90Days.put("playMedian", userData.getNonCommercialPlayMedian90Days());
            nonCommercial90Days.put("completionRate", userData.getNonCommercialCompletionRate90Days());
            nonCommercial90Days.put("interactionRate", userData.getNonCommercialInteractionRate90Days());
            nonCommercial90Days.put("interactionVolume", userData.getNonCommercialInteractionVolume90Days());
            nonCommercial90Days.put("postedWorksCount", userData.getNonCommercialPostedWorksCount90Days());
            nonCommercial90Days.put("averageDuration", userData.getNonCommercialAverageDuration90Days());
            nonCommercial90Days.put("averageForward", userData.getNonCommercialAverageForward90Days());
            nonCommercial90Days.put("averageComment", userData.getNonCommercialAverageComment90Days());
            nonCommercial90Days.put("averageLike", userData.getNonCommercialAverageLike90Days());

            // 检查非商单相关字段（30天）
            Map<String, Object> nonCommercial30Days = new HashMap<>();
            nonCommercial30Days.put("playMedian", userData.getNonCommercialPlayMedian30Days());
            nonCommercial30Days.put("completionRate", userData.getNonCommercialCompletionRate30Days());
            nonCommercial30Days.put("interactionRate", userData.getNonCommercialInteractionRate30Days());
            nonCommercial30Days.put("interactionVolume", userData.getNonCommercialInteractionVolume30Days());
            nonCommercial30Days.put("postedWorksCount", userData.getNonCommercialPostedWorksCount30Days());
            nonCommercial30Days.put("averageDuration", userData.getNonCommercialAverageDuration30Days());
            nonCommercial30Days.put("averageForward", userData.getNonCommercialAverageForward30Days());
            nonCommercial30Days.put("averageComment", userData.getNonCommercialAverageComment30Days());
            nonCommercial30Days.put("averageLike", userData.getNonCommercialAverageLike30Days());

            // 检查商单相关字段（90天）
            Map<String, Object> commercial90Days = new HashMap<>();
            commercial90Days.put("playMedian", userData.getCommercialPlayMedian90Days());
            commercial90Days.put("completionRate", userData.getCommercialCompletionRate90Days());
            commercial90Days.put("interactionRate", userData.getCommercialInteractionRate90Days());
            commercial90Days.put("interactionVolume", userData.getCommercialInteractionVolume90Days());
            commercial90Days.put("postedWorksCount", userData.getCommercialPostedWorksCount90Days());
            commercial90Days.put("averageDuration", userData.getCommercialAverageDuration90Days());
            commercial90Days.put("averageForward", userData.getCommercialAverageForward90Days());
            commercial90Days.put("averageComment", userData.getCommercialAverageComment90Days());
            commercial90Days.put("averageLike", userData.getCommercialAverageLike90Days());

            // 检查商单相关字段（30天）
            Map<String, Object> commercial30Days = new HashMap<>();
            commercial30Days.put("playMedian", userData.getCommercialPlayMedian30Days());
            commercial30Days.put("completionRate", userData.getCommercialCompletionRate30Days());
            commercial30Days.put("interactionRate", userData.getCommercialInteractionRate30Days());
            commercial30Days.put("interactionVolume", userData.getCommercialInteractionVolume30Days());
            commercial30Days.put("postedWorksCount", userData.getCommercialPostedWorksCount30Days());
            commercial30Days.put("averageDuration", userData.getCommercialAverageDuration30Days());
            commercial30Days.put("averageForward", userData.getCommercialAverageForward30Days());
            commercial30Days.put("averageComment", userData.getCommercialAverageComment30Days());
            commercial30Days.put("averageLike", userData.getCommercialAverageLike30Days());

            // 检查内容标签字段
            Map<String, Object> contentTags = new HashMap<>();
            contentTags.put("contentSecondLevelTagForCommercial", userData.getContentSecondLevelTagForCommercial());
            contentTags.put("contentFirstLevelTagForCommercial", userData.getContentFirstLevelTagForCommercial());
            contentTags.put("contentFirstLevelTagForNonCommercial", userData.getContentFirstLevelTagForNonCommercial());
            contentTags.put("contentSecondLevelTagForNonCommercial", userData.getContentSecondLevelTagForNonCommercial());

            result.put("nonCommercial90Days", nonCommercial90Days);
            result.put("nonCommercial30Days", nonCommercial30Days);
            result.put("commercial90Days", commercial90Days);
            result.put("commercial30Days", commercial30Days);
            result.put("contentTags", contentTags);

            // 统计哪些字段有数据
            Map<String, Boolean> dataStatus = new HashMap<>();
            dataStatus.put("hasNonCommercial90DaysData", userData.getNonCommercialPlayMedian90Days() != null);
            dataStatus.put("hasNonCommercial30DaysData", userData.getNonCommercialPlayMedian30Days() != null);
            dataStatus.put("hasCommercial90DaysData", userData.getCommercialPlayMedian90Days() != null);
            dataStatus.put("hasCommercial30DaysData", userData.getCommercialPlayMedian30Days() != null);
            dataStatus.put("hasContentTagsData", StringUtils.hasText(userData.getContentSecondLevelTagForCommercial()));

            result.put("dataStatus", dataStatus);

            return ApiResponse.success(result, "新增字段数据查询成功");

        } catch (Exception e) {
            logger.error("测试新增字段失败: userId={}, error={}", userId, e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试 JSON 字段数据处理
     * @param userId 用户ID（可选）
     * @return JSON 字段数据详情
     */
    @GetMapping("/test-json-fields")
    public ApiResponse<Map<String, Object>> testJsonFields(@RequestParam(required = false) String userId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 确定要查询的表名
            String tableName = oppoDataService.findLatestTableWithData();
            result.put("tableName", tableName);

            // 构建查询条件
            String queryUserId = userId;
            if (!StringUtils.hasText(queryUserId)) {
                // 如果没有指定用户ID，查询第一个有画像数据的用户
                List<OppoData> dataList = oppoDataService.findUsersWithPortraitData(tableName, 1);
                if (!dataList.isEmpty()) {
                    queryUserId = dataList.get(0).getUserId();
                } else {
                    return ApiResponse.success(result, "未找到有画像数据的用户", "204");
                }
            }

            // 查询指定用户的数据
            OppoData userData = oppoDataService.findByUserId(tableName, queryUserId);
            if (userData == null) {
                return ApiResponse.error("未找到指定用户: " + queryUserId, "404");
            }

            result.put("userId", queryUserId);
            result.put("userNickname", userData.getUserNickname());

            // 检查 JSON 字段的数据类型和格式
            Map<String, Object> jsonFieldsInfo = new HashMap<>();

            // 粉丝画像 JSON 字段
            String fansDistribution = userData.getObjectStarObjectFansDistribution();
            Map<String, Object> fansInfo = new HashMap<>();
            fansInfo.put("hasData", fansDistribution != null);
            fansInfo.put("dataType", fansDistribution != null ? fansDistribution.getClass().getSimpleName() : "null");
            if (fansDistribution != null) {
                fansInfo.put("dataLength", fansDistribution.length());
                fansInfo.put("isValidJson", isValidJson(fansDistribution));
                fansInfo.put("preview", fansDistribution.length() > 100 ?
                    fansDistribution.substring(0, 100) + "..." : fansDistribution);
            }
            jsonFieldsInfo.put("fansDistribution", fansInfo);

            // 观众画像 JSON 字段
            String viewersDistribution = userData.getObjectStarObjectViewersDistribution();
            Map<String, Object> viewersInfo = new HashMap<>();
            viewersInfo.put("hasData", viewersDistribution != null);
            viewersInfo.put("dataType", viewersDistribution != null ? viewersDistribution.getClass().getSimpleName() : "null");
            if (viewersDistribution != null) {
                viewersInfo.put("dataLength", viewersDistribution.length());
                viewersInfo.put("isValidJson", isValidJson(viewersDistribution));
                viewersInfo.put("preview", viewersDistribution.length() > 100 ?
                    viewersDistribution.substring(0, 100) + "..." : viewersDistribution);
            }
            jsonFieldsInfo.put("viewersDistribution", viewersInfo);

            result.put("jsonFieldsInfo", jsonFieldsInfo);

            return ApiResponse.success(result, "JSON 字段数据检查完成");

        } catch (Exception e) {
            logger.error("测试 JSON 字段失败: userId={}, error={}", userId, e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 检查字符串是否是有效的 JSON
     */
    private boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        try {
            com.alibaba.fastjson2.JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


}
