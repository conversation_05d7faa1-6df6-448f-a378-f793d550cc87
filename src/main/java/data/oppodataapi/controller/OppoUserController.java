package data.oppodataapi.controller;

import data.oppodataapi.dto.ApiResponse;
import data.oppodataapi.dto.OppoUserUploadRequest;
import data.oppodataapi.dto.OppoUserUploadResponse;
import data.oppodataapi.entity.OppoUser;
import data.oppodataapi.entity.OppoUserBatch;
import data.oppodataapi.service.OppoUserService;
import data.oppodataapi.mapper.OppoUserBatchMapper;
import data.oppodataapi.util.ExcelUtil;
import data.oppodataapi.util.UserWeeklyTableUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.Arrays;

/**
 * OPPO用户控制器
 */
@RestController
@RequestMapping("/api/oppo/user")
@CrossOrigin(origins = "*")
public class OppoUserController {
    
    private static final Logger logger = LoggerFactory.getLogger(OppoUserController.class);
    
    @Autowired
    private OppoUserService oppoUserService;

    @Autowired
    private OppoUserBatchMapper oppoUserBatchMapper;
    
    /**
     * 批量上传用户数据
     * @param request 上传请求
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ApiResponse<OppoUserUploadResponse> uploadUsers(@RequestBody OppoUserUploadRequest request) {
        try {
            logger.info("接收到用户数据上传请求，数据量: {}", 
                       request.getUsers() != null ? request.getUsers().size() : 0);
            
            // 参数校验
            if (request.getUsers() == null || request.getUsers().isEmpty()) {
                return ApiResponse.error("用户数据列表不能为空");
            }
            
            if (request.getUsers().size() > 10000) {
                return ApiResponse.error("单次上传数据量不能超过10000条");
            }
            
            // 调用服务进行上传
            OppoUserUploadResponse result = oppoUserService.uploadUsers(request);
            
            logger.info("用户数据上传完成，总计: {}, 成功: {}, 失败: {}", 
                       result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());
            
            return ApiResponse.success(result, "用户数据上传完成");
            
        } catch (RuntimeException e) {
            // Token校验失败或其他业务异常
            logger.error("用户数据上传失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            logger.error("用户数据上传失败: {}", e.getMessage(), e);
            return ApiResponse.error("用户数据上传失败: " + e.getMessage());
        }
    }

    /**
     * 通过 Excel 文件批量上传用户数据
     * @param file Excel 文件
     * @param token 认证令牌
     * @param overwrite 是否覆盖已存在数据（默认false，避免重复数据）
     * @param batchSize 批次大小
     * @return 上传结果
     */
    @PostMapping("/upload-excel")
    public ApiResponse<OppoUserUploadResponse> uploadUsersFromExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("token") String token,
            @RequestParam(value = "overwrite", defaultValue = "false") Boolean overwrite,
            @RequestParam(value = "batchSize", defaultValue = "100") Integer batchSize) {
        try {
            logger.info("接收到 Excel 文件上传请求，文件名: {}, 大小: {} bytes",
                       file.getOriginalFilename(), file.getSize());

            // 文件大小校验（限制为 50MB）
            if (file.getSize() > 50 * 1024 * 1024) {
                return ApiResponse.error("文件大小不能超过 50MB");
            }

            // 文件格式校验
            if (!ExcelUtil.isValidExcelFile(file)) {
                return ApiResponse.error("文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件");
            }

            // 调用服务进行上传
            OppoUserUploadResponse result = oppoUserService.uploadUsersFromExcel(file, token, overwrite, batchSize);

            logger.info("Excel 文件上传完成，总计: {}, 成功: {}, 失败: {}",
                       result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

            return ApiResponse.success(result, "Excel 文件上传完成");

        } catch (RuntimeException e) {
            // Token校验失败或其他业务异常
            logger.error("Excel 文件上传失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            logger.error("Excel 文件上传失败: {}", e.getMessage(), e);
            return ApiResponse.error("Excel 文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取 Excel 文件格式说明
     * @return 格式说明
     */
    @GetMapping("/excel-format")
    public ApiResponse<String> getExcelFormat() {
        try {
            String formatDescription = ExcelUtil.getExcelFormatDescription();
            return ApiResponse.success(formatDescription, "获取 Excel 格式说明成功");
        } catch (Exception e) {
            logger.error("获取 Excel 格式说明失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取 Excel 格式说明失败: " + e.getMessage());
        }
    }

    /**
     * 下载 Excel 模板文件
     * @param
     */
    @GetMapping("/download-template")
    public void downloadExcelTemplate(HttpServletResponse response) {
        try {
            logger.info("接收到下载 Excel 模板文件请求");
            ExcelUtil.generateExcelTemplate(response);
            logger.info("Excel 模板文件下载成功");
        } catch (IOException e) {
            logger.error("下载 Excel 模板文件失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("下载模板文件失败: " + e.getMessage());
            } catch (IOException ex) {
                logger.error("写入错误响应失败: {}", ex.getMessage(), ex);
            }
        }
    }

    /**
     * 测试Excel上传功能（简化版）
     * @param file Excel文件
     * @return 测试结果
     */
    @PostMapping("/test-upload-excel")
    public ApiResponse<String> testUploadExcel(@RequestParam("file") MultipartFile file) {
        try {
            logger.info("接收到测试Excel上传请求，文件名: {}, 大小: {} bytes",
                       file.getOriginalFilename(), file.getSize());

            // 基本文件检查
            if (file.isEmpty()) {
                return ApiResponse.error("文件为空");
            }

            // 文件格式检查
            if (!ExcelUtil.isValidExcelFile(file)) {
                return ApiResponse.error("文件格式不正确，请上传 .xlsx 或 .xls 格式的 Excel 文件");
            }

            // 尝试读取Excel内容
            List<OppoUserUploadRequest.OppoUserData> userData = ExcelUtil.readUsersFromExcel(file);

            return ApiResponse.success("测试成功，读取到 " + userData.size() + " 条数据",
                                     "Excel文件解析成功");

        } catch (Exception e) {
            logger.error("测试Excel上传失败: {}", e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 诊断接口 - 检查系统状态
     * @return 系统状态
     */
    @GetMapping("/diagnose")
    public ApiResponse<Map<String, Object>> diagnose() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 检查服务注入
            status.put("oppoUserService", oppoUserService != null ? "已注入" : "未注入");
            status.put("oppoUserBatchMapper", oppoUserBatchMapper != null ? "已注入" : "未注入");

            // 检查当前周表名
            String currentWeekTable = data.oppodataapi.util.UserWeeklyTableUtil.getCurrentWeekTableName();
            status.put("currentWeekTable", currentWeekTable);

            // 检查表是否存在
            try {
                int tableExists = oppoUserService.checkTableExists(currentWeekTable);
                status.put("tableExists", tableExists > 0);
            } catch (Exception e) {
                status.put("tableCheckError", e.getMessage());
            }

            status.put("timestamp", java.time.LocalDateTime.now());
            status.put("status", "OK");

            return ApiResponse.success(status, "诊断完成");

        } catch (Exception e) {
            logger.error("诊断失败: {}", e.getMessage(), e);
            status.put("error", e.getMessage());
            status.put("status", "ERROR");
            return ApiResponse.error("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 根据批次ID查询用户列表
     * @param batchId 批次ID
     * @return 用户列表
     */
    @GetMapping("/batch/{batchId}/users")
    public ApiResponse<List<OppoUser>> getUsersByBatchId(@PathVariable String batchId) {
        try {
            logger.info("接收到根据批次ID查询用户请求: {}", batchId);

            List<OppoUser> users = oppoUserService.getUsersByBatchId(batchId);

            logger.info("批次 {} 查询到 {} 个用户", batchId, users.size());
            return ApiResponse.success(users, "查询成功");

        } catch (Exception e) {
            logger.error("根据批次ID查询用户失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

 
    /**
     * 获取批次信息
     * @param batchId 批次ID
     * @return 批次信息
     */
    @GetMapping("/batch/{batchId}/info")
    public ApiResponse<OppoUserBatch> getBatchInfo(@PathVariable String batchId) {
        try {
            logger.info("接收到获取批次信息请求: {}", batchId);

            OppoUserBatch batch = oppoUserBatchMapper.selectByBatchId(batchId);

            if (batch != null) {
                return ApiResponse.success(batch, "获取批次信息成功");
            } else {
                return ApiResponse.success(null, "批次不存在");
            }

        } catch (Exception e) {
            logger.error("获取批次信息失败: batchId={}, error={}", batchId, e.getMessage(), e);
            return ApiResponse.error("获取批次信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的批次列表
     * @param limit 限制数量
     * @return 批次列表
     */
    @GetMapping("/batches/recent")
    public ApiResponse<List<OppoUserBatch>> getRecentBatches(
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        try {
            logger.info("接收到获取最近批次列表请求，限制数量: {}", limit);

            if (limit > 100) {
                limit = 100; // 最多返回100条记录
            }

            List<OppoUserBatch> batches = oppoUserBatchMapper.selectRecentBatches(limit);

            return ApiResponse.success(batches, "获取批次列表成功");

        } catch (Exception e) {
            logger.error("获取批次列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取批次列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据上传来源获取批次列表
     * @param uploadSource 上传来源
     * @param limit 限制数量
     * @return 批次列表
     */
    @GetMapping("/batches/source/{uploadSource}")
    public ApiResponse<List<OppoUserBatch>> getBatchesBySource(
            @PathVariable String uploadSource,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        try {
            logger.info("接收到根据上传来源获取批次列表请求: {}, 限制数量: {}", uploadSource, limit);

            if (limit > 100) {
                limit = 100;
            }

            List<OppoUserBatch> batches = oppoUserBatchMapper.selectByUploadSource(uploadSource, limit);

            return ApiResponse.success(batches, "获取批次列表成功");

        } catch (Exception e) {
            logger.error("根据上传来源获取批次列表失败: uploadSource={}, error={}", uploadSource, e.getMessage(), e);
            return ApiResponse.error("获取批次列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据抖音ID查询用户
     * @param douyinId 抖音ID
     * @return 用户信息
     */
    @GetMapping("/douyin/{douyinId}")
    public ApiResponse<OppoUser> getUserByDouyinId(@PathVariable String douyinId) {
        try {
            logger.info("接收到根据抖音ID查询用户请求: {}", douyinId);
            
            OppoUser user = oppoUserService.getUserByDouyinId(douyinId);
            
            if (user != null) {
                logger.info("查询到用户信息: douyinId={}, starMapId={}", user.getDouyinId(), user.getStarMapId());
                return ApiResponse.success(user, "查询成功");
            } else {
                logger.info("未找到抖音ID为 {} 的用户", douyinId);
                return ApiResponse.success(null, "未找到用户");
            }
            
        } catch (Exception e) {
            logger.error("根据抖音ID查询用户失败: douyinId={}, error={}", douyinId, e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据星图ID查询用户
     * @param starMapId 星图ID
     * @return 用户信息
     */
    @GetMapping("/starmap/{starMapId}")
    public ApiResponse<OppoUser> getUserByStarMapId(@PathVariable String starMapId) {
        try {
            logger.info("接收到根据星图ID查询用户请求: {}", starMapId);
            
            OppoUser user = oppoUserService.getUserByStarMapId(starMapId);
            
            if (user != null) {
                logger.info("查询到用户信息: douyinId={}, starMapId={}", user.getDouyinId(), user.getStarMapId());
                return ApiResponse.success(user, "查询成功");
            } else {
                logger.info("未找到星图ID为 {} 的用户", starMapId);
                return ApiResponse.success(null, "未找到用户");
            }
            
        } catch (Exception e) {
            logger.error("根据星图ID查询用户失败: starMapId={}, error={}", starMapId, e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     * @return 健康状态
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("OPPO User Service is running", "服务正常");
    }

    /**
     * 测试覆盖功能
     * @param overwrite 是否覆盖
     * @return 测试结果
     */
    @PostMapping("/test-overwrite")
    public ApiResponse<Map<String, Object>> testOverwrite(@RequestParam(defaultValue = "false") Boolean overwrite) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 创建测试数据
            OppoUserUploadRequest request = new OppoUserUploadRequest();
            request.setToken("test-token");
            request.setOverwrite(overwrite);

            List<OppoUserUploadRequest.OppoUserData> testUsers = new ArrayList<>();

            // 添加测试用户（这些用户可能已经存在）
            OppoUserUploadRequest.OppoUserData user1 = new OppoUserUploadRequest.OppoUserData();
            user1.setDouyinId("test_overwrite_001");
            user1.setStarMapId("test_overwrite_starmap_001");
            testUsers.add(user1);

            OppoUserUploadRequest.OppoUserData user2 = new OppoUserUploadRequest.OppoUserData();
            user2.setDouyinId("test_overwrite_002");
            user2.setStarMapId("test_overwrite_starmap_002");
            testUsers.add(user2);

            request.setUsers(testUsers);

            // 第一次上传
            logger.info("第一次上传测试数据，覆盖模式: {}", overwrite);
            OppoUserUploadResponse firstUpload = oppoUserService.uploadUsers(request);

            // 等待1秒
            Thread.sleep(1000);

            // 第二次上传相同数据
            logger.info("第二次上传相同数据，覆盖模式: {}", overwrite);
            OppoUserUploadResponse secondUpload = oppoUserService.uploadUsers(request);

            result.put("overwriteMode", overwrite);
            result.put("firstUpload", firstUpload);
            result.put("secondUpload", secondUpload);
            result.put("expectedBehavior", overwrite ? "第二次应该更新现有记录" : "第二次应该忽略重复记录");

            // 查询数据库验证结果
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            int totalRecordsAfterFirst = oppoUserService.countByBatchId(currentWeekTable, firstUpload.getBatchId());
            int totalRecordsAfterSecond = oppoUserService.countByBatchId(currentWeekTable, secondUpload.getBatchId());

            result.put("verification", Map.of(
                "currentWeekTable", currentWeekTable,
                "recordsAfterFirstUpload", totalRecordsAfterFirst,
                "recordsAfterSecondUpload", totalRecordsAfterSecond,
                "totalRecordsInTable", totalRecordsAfterFirst + totalRecordsAfterSecond
            ));

            return ApiResponse.success(result, "覆盖功能测试完成");

        } catch (Exception e) {
            logger.error("测试覆盖功能失败: {}", e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 诊断上传数据重复情况
     * @param file Excel文件
     * @return 诊断结果
     */
    @PostMapping("/diagnose-duplicates")
    public ApiResponse<Map<String, Object>> diagnoseDuplicates(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 读取Excel数据
            List<OppoUserUploadRequest.OppoUserData> userData = ExcelUtil.readUsersFromExcel(file);

            // 分析重复情况
            Map<String, Integer> douyinIdCount = new HashMap<>();
            Map<String, Integer> starMapIdCount = new HashMap<>();
            Set<String> duplicateDouyinIds = new HashSet<>();
            Set<String> duplicateStarMapIds = new HashSet<>();

            for (OppoUserUploadRequest.OppoUserData data : userData) {
                // 统计抖音ID
                if (StringUtils.hasText(data.getDouyinId())) {
                    douyinIdCount.put(data.getDouyinId(), douyinIdCount.getOrDefault(data.getDouyinId(), 0) + 1);
                    if (douyinIdCount.get(data.getDouyinId()) > 1) {
                        duplicateDouyinIds.add(data.getDouyinId());
                    }
                }

                // 统计星图ID
                if (StringUtils.hasText(data.getStarMapId())) {
                    starMapIdCount.put(data.getStarMapId(), starMapIdCount.getOrDefault(data.getStarMapId(), 0) + 1);
                    if (starMapIdCount.get(data.getStarMapId()) > 1) {
                        duplicateStarMapIds.add(data.getStarMapId());
                    }
                }
            }

            // 检查数据库中已存在的数据
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            List<String> allDouyinIds = userData.stream()
                .map(OppoUserUploadRequest.OppoUserData::getDouyinId)
                .filter(StringUtils::hasText)
                .collect(java.util.stream.Collectors.toList());

            List<String> allStarMapIds = userData.stream()
                .map(OppoUserUploadRequest.OppoUserData::getStarMapId)
                .filter(StringUtils::hasText)
                .collect(java.util.stream.Collectors.toList());

            // 查询数据库中已存在的记录
            int existingDouyinCount = 0;
            int existingStarMapCount = 0;

            if (!allDouyinIds.isEmpty()) {
                existingDouyinCount = oppoUserService.countExistingByDouyinIds(currentWeekTable, allDouyinIds);
            }

            if (!allStarMapIds.isEmpty()) {
                existingStarMapCount = oppoUserService.countExistingByStarMapIds(currentWeekTable, allStarMapIds);
            }

            result.put("fileName", file.getOriginalFilename());
            result.put("totalRecords", userData.size());
            result.put("currentWeekTable", currentWeekTable);

            // Excel内部重复统计
            result.put("internalDuplicates", Map.of(
                "douyinIdDuplicates", duplicateDouyinIds.size(),
                "starMapIdDuplicates", duplicateStarMapIds.size(),
                "duplicateDouyinIds", duplicateDouyinIds,
                "duplicateStarMapIds", duplicateStarMapIds
            ));

            // 数据库重复统计
            result.put("databaseDuplicates", Map.of(
                "existingDouyinIds", existingDouyinCount,
                "existingStarMapIds", existingStarMapCount
            ));

            // 预期结果分析
            int expectedInserts = userData.size() - duplicateDouyinIds.size() - duplicateStarMapIds.size() - existingDouyinCount - existingStarMapCount;
            result.put("expectedInserts", Math.max(0, expectedInserts));

            return ApiResponse.success(result, "重复数据诊断完成");

        } catch (Exception e) {
            logger.error("诊断重复数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 测试 update_time 字段更新
     * @param overwrite 是否覆盖
     * @return 测试结果
     */
    @PostMapping("/test-update-time")
    public ApiResponse<Map<String, Object>> testUpdateTime(@RequestParam(defaultValue = "false") Boolean overwrite) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 创建测试数据
            OppoUserUploadRequest request = new OppoUserUploadRequest();
            request.setToken("test-token");
            request.setOverwrite(overwrite);

            List<OppoUserUploadRequest.OppoUserData> testUsers = new ArrayList<>();

            // 添加测试用户
            OppoUserUploadRequest.OppoUserData user1 = new OppoUserUploadRequest.OppoUserData();
            user1.setDouyinId("test_update_time_001");
            user1.setStarMapId("test_update_time_starmap_001");
            testUsers.add(user1);

            OppoUserUploadRequest.OppoUserData user2 = new OppoUserUploadRequest.OppoUserData();
            user2.setDouyinId("test_update_time_002");
            user2.setStarMapId("test_update_time_starmap_002");
            testUsers.add(user2);

            request.setUsers(testUsers);

            // 记录开始时间
            LocalDateTime startTime = LocalDateTime.now();

            // 第一次上传
            logger.info("第一次上传测试数据，覆盖模式: {}", overwrite);
            OppoUserUploadResponse firstUpload = oppoUserService.uploadUsers(request);

            // 等待2秒确保时间差异
            Thread.sleep(2000);

            // 第二次上传相同数据
            logger.info("第二次上传相同数据，覆盖模式: {}", overwrite);
            OppoUserUploadResponse secondUpload = oppoUserService.uploadUsers(request);

            // 记录结束时间
            LocalDateTime endTime = LocalDateTime.now();

            // 查询数据库中的实际记录，检查 update_time
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            List<OppoUser> dbRecords = oppoUserService.findByDouyinIds(currentWeekTable,
                Arrays.asList("test_update_time_001", "test_update_time_002"));

            // 分析时间更新情况
            List<Map<String, Object>> timeAnalysis = new ArrayList<>();
            for (OppoUser record : dbRecords) {
                Map<String, Object> analysis = new HashMap<>();
                analysis.put("douyinId", record.getDouyinId());
                analysis.put("createTime", record.getCreateTime());
                analysis.put("updateTime", record.getUpdateTime());
                analysis.put("batchId", record.getBatchId());
                analysis.put("timeUpdatedCorrectly", record.getUpdateTime().isAfter(startTime));
                timeAnalysis.add(analysis);
            }

            result.put("overwriteMode", overwrite);
            result.put("startTime", startTime);
            result.put("endTime", endTime);
            result.put("firstUpload", firstUpload);
            result.put("secondUpload", secondUpload);
            result.put("currentWeekTable", currentWeekTable);
            result.put("dbRecords", timeAnalysis);
            result.put("expectedBehavior", overwrite ?
                "update_time 应该在第二次上传时更新" :
                "如果是重复记录，update_time 可能不变");

            return ApiResponse.success(result, "update_time 测试完成");

        } catch (Exception e) {
            logger.error("测试 update_time 失败: {}", e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试用户去重功能
     * @param douyinId 抖音ID（可选）
     * @return 用户去重信息
     */
    @GetMapping("/test-deduplication")
    public ApiResponse<Map<String, Object>> testUserDeduplication(@RequestParam(required = false) String douyinId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取当前周表名
            String currentWeekTable = UserWeeklyTableUtil.getCurrentWeekTableName();
            result.put("currentWeekTable", currentWeekTable);

            if (StringUtils.hasText(douyinId)) {
                // 查询指定用户的重复情况
                List<OppoUser> duplicateUsers = oppoUserService.findByDouyinId(currentWeekTable, douyinId);
                result.put("douyinId", douyinId);
                result.put("duplicateCount", duplicateUsers.size());
                result.put("duplicateUsers", duplicateUsers);

                if (duplicateUsers.size() > 1) {
                    result.put("hasDuplicates", true);
                    result.put("message", "发现重复用户数据");
                } else if (duplicateUsers.size() == 1) {
                    result.put("hasDuplicates", false);
                    result.put("message", "用户数据唯一");
                } else {
                    result.put("hasDuplicates", false);
                    result.put("message", "未找到用户数据");
                }
            } else {
                // 统计整体的重复情况
                Map<String, Object> stats = new HashMap<>();

                // 获取总记录数
                int totalRecords = oppoUserService.getTotalRecordCount(currentWeekTable);
                stats.put("totalRecords", totalRecords);

                // 获取唯一抖音ID数量
                int uniqueDouyinIds = oppoUserService.getUniqueDouyinIdCount(currentWeekTable);
                stats.put("uniqueDouyinIds", uniqueDouyinIds);

                // 获取唯一星图ID数量
                int uniqueStarMapIds = oppoUserService.getUniqueStarMapIdCount(currentWeekTable);
                stats.put("uniqueStarMapIds", uniqueStarMapIds);

                // 计算重复率
                stats.put("douyinIdDuplicationRate", totalRecords > 0 ?
                    String.format("%.2f%%", (totalRecords - uniqueDouyinIds) * 100.0 / totalRecords) : "0%");
                stats.put("starMapIdDuplicationRate", totalRecords > 0 ?
                    String.format("%.2f%%", (totalRecords - uniqueStarMapIds) * 100.0 / totalRecords) : "0%");

                result.putAll(stats);
                result.put("message", "整体去重统计完成");
            }

            return ApiResponse.success(result, "用户去重信息查询成功");

        } catch (Exception e) {
            logger.error("测试用户去重功能失败: douyinId={}, error={}", douyinId, e.getMessage(), e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }
}
