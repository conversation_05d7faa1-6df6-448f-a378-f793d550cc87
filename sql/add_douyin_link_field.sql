-- 为 OPPO 用户表添加 douyin_link 字段
-- 需要为所有现有的周表和未来的周表添加此字段

-- 1. 为现有的周表添加 douyin_link 字段
-- 2025年第1周到第20周
ALTER TABLE `oppo_user_2025_01` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_02` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_03` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_04` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_05` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_06` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_07` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_08` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_09` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_10` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_11` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_12` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_13` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_14` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_15` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_16` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_17` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_18` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_19` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;
ALTER TABLE `oppo_user_2025_20` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接' AFTER `star_map_id`;

-- 2. 更新周表创建存储过程，确保新创建的表包含 douyin_link 字段
DELIMITER $$

-- 删除旧的存储过程
DROP PROCEDURE IF EXISTS CreateWeeklyUserTable$$

-- 重新创建包含 douyin_link 字段的存储过程
CREATE PROCEDURE CreateWeeklyUserTable(IN year INT, IN week INT)
BEGIN
    DECLARE table_name VARCHAR(50);
    DECLARE sql_stmt TEXT;
    
    -- 生成表名，格式：oppo_user_YYYY_WW
    SET table_name = CONCAT('oppo_user_', year, '_', LPAD(week, 2, '0'));
    
    -- 构建创建表的SQL语句（包含 douyin_link 字段）
    SET sql_stmt = CONCAT('
        CREATE TABLE IF NOT EXISTS `', table_name, '` (
          `id` int NOT NULL AUTO_INCREMENT,
          `douyin_id` varchar(100) DEFAULT NULL COMMENT ''抖音ID'',
          `star_map_id` varchar(100) DEFAULT NULL COMMENT ''星图ID'',
          `douyin_link` varchar(500) DEFAULT NULL COMMENT ''抖音链接'',
          `batch_id` varchar(50) NOT NULL COMMENT ''批次ID'',
          `batch_name` varchar(200) DEFAULT NULL COMMENT ''批次名称'',
          `upload_source` varchar(50) DEFAULT ''EXCEL'' COMMENT ''上传来源：EXCEL, API, MANUAL'',
          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',
          `deleted` int DEFAULT 0 COMMENT ''删除标记：0-未删除，1-已删除'',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_douyin_id` (`douyin_id`),
          UNIQUE KEY `uk_star_map_id` (`star_map_id`),
          KEY `idx_batch_id` (`batch_id`),
          KEY `idx_create_time` (`create_time`),
          KEY `idx_deleted` (`deleted`),
          KEY `idx_upload_source` (`upload_source`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=''OPPO用户周表-', year, '年第', week, '周''
    ');
    
    -- 执行SQL语句
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT CONCAT('周表 ', table_name, ' 创建成功（包含 douyin_link 字段）') AS result;
END$$

DELIMITER ;

-- 3. 验证字段是否添加成功
-- 查看表结构（以第1周表为例）
DESCRIBE `oppo_user_2025_01`;

-- 4. 创建一个通用的添加字段脚本（用于未来可能遗漏的表）
DELIMITER $$

CREATE PROCEDURE AddDouyinLinkToUserTable(IN table_name VARCHAR(50))
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    -- 检查字段是否已存在
    SELECT COUNT(*) INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = table_name 
      AND COLUMN_NAME = 'douyin_link';
    
    -- 如果字段不存在，则添加
    IF column_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', table_name, '` ADD COLUMN `douyin_link` varchar(500) DEFAULT NULL COMMENT ''抖音链接'' AFTER `star_map_id`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SELECT CONCAT('字段 douyin_link 已添加到表 ', table_name) AS result;
    ELSE
        SELECT CONCAT('表 ', table_name, ' 中已存在 douyin_link 字段') AS result;
    END IF;
END$$

DELIMITER ;

-- 5. 批量为所有用户表添加 douyin_link 字段（防止遗漏）
DELIMITER $$

CREATE PROCEDURE AddDouyinLinkToAllUserTables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    
    -- 声明游标
    DECLARE table_cursor CURSOR FOR 
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME LIKE 'oppo_user_%'
          AND TABLE_NAME NOT LIKE '%batch%';
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN table_cursor;
    
    -- 循环处理每个表
    read_loop: LOOP
        FETCH table_cursor INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 为当前表添加字段
        CALL AddDouyinLinkToUserTable(table_name);
    END LOOP;
    
    -- 关闭游标
    CLOSE table_cursor;
    
    SELECT '所有用户表的 douyin_link 字段添加完成' AS result;
END$$

DELIMITER ;

-- 6. 执行批量添加（可选，如果上面的单独 ALTER 语句执行失败）
-- CALL AddDouyinLinkToAllUserTables();

-- 7. 验证所有表都包含 douyin_link 字段
SELECT 
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    CHARACTER_MAXIMUM_LENGTH as '最大长度',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME LIKE 'oppo_user_2025_%'
  AND COLUMN_NAME = 'douyin_link'
ORDER BY TABLE_NAME;

-- 8. 查看更新后的表结构示例
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    CHARACTER_MAXIMUM_LENGTH as '最大长度',
    IS_NULLABLE as '是否可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'oppo_user_2025_01'
ORDER BY ORDINAL_POSITION;
