-- OPPO用户周表创建脚本
-- 采用按周分表的策略，每周一个表，与oppo_data保持一致
-- 表名格式：oppo_user_YYYY_WW (年份_周数)
-- 示例：oppo_user_2025_01, oppo_user_2025_02 等

-- 创建当前周的表（示例：2025年第1周）
CREATE TABLE IF NOT EXISTS `oppo_user_2025_01` (
  `id` int NOT NULL AUTO_INCREMENT,
  `douyin_id` varchar(100) DEFAULT NULL COMMENT '抖音ID',
  `star_map_id` varchar(100) DEFAULT NULL COMMENT '星图ID',
  `douyin_link` varchar(500) DEFAULT NULL COMMENT '抖音链接',
  `batch_id` varchar(50) NOT NULL COMMENT '批次ID',
  `batch_name` varchar(200) DEFAULT NULL COMMENT '批次名称',
  `upload_source` varchar(50) DEFAULT 'EXCEL' COMMENT '上传来源：EXCEL, API, MANUAL',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_douyin_id` (`douyin_id`),
  UNIQUE KEY `uk_star_map_id` (`star_map_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_upload_source` (`upload_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='OPPO用户周表-2025年第1周';

-- 创建更多周表（根据需要创建）
-- 2025年第2周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_02` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_02` COMMENT='OPPO用户周表-2025年第2周';

-- 2025年第3周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_03` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_03` COMMENT='OPPO用户周表-2025年第3周';

-- 2025年第4周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_04` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_04` COMMENT='OPPO用户周表-2025年第4周';

-- 2025年第5周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_05` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_05` COMMENT='OPPO用户周表-2025年第5周';

-- 2025年第6周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_06` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_06` COMMENT='OPPO用户周表-2025年第6周';

-- 2025年第7周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_07` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_07` COMMENT='OPPO用户周表-2025年第7周';

-- 2025年第8周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_08` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_08` COMMENT='OPPO用户周表-2025年第8周';

-- 2025年第9周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_09` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_09` COMMENT='OPPO用户周表-2025年第9周';

-- 2025年第10周
CREATE TABLE IF NOT EXISTS `oppo_user_2025_10` LIKE `oppo_user_2025_01`;
ALTER TABLE `oppo_user_2025_10` COMMENT='OPPO用户周表-2025年第10周';



-- 创建周表管理存储过程
DELIMITER $$

-- 创建指定年份和周数的用户周表
CREATE PROCEDURE IF NOT EXISTS CreateWeeklyUserTable(IN year INT, IN week INT)
BEGIN
    DECLARE table_name VARCHAR(50);
    DECLARE sql_stmt TEXT;
    
    -- 生成表名，格式：oppo_user_YYYY_WW
    SET table_name = CONCAT('oppo_user_', year, '_', LPAD(week, 2, '0'));
    
    -- 构建创建表的SQL语句
    SET sql_stmt = CONCAT('
        CREATE TABLE IF NOT EXISTS `', table_name, '` (
          `id` int NOT NULL AUTO_INCREMENT,
          `douyin_id` varchar(100) DEFAULT NULL COMMENT ''抖音ID'',
          `star_map_id` varchar(100) DEFAULT NULL COMMENT ''星图ID'',
          `douyin_link` varchar(500) DEFAULT NULL COMMENT ''抖音链接'',
          `batch_id` varchar(50) NOT NULL COMMENT ''批次ID'',
          `batch_name` varchar(200) DEFAULT NULL COMMENT ''批次名称'',
          `upload_source` varchar(50) DEFAULT ''EXCEL'' COMMENT ''上传来源：EXCEL, API, MANUAL'',
          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',
          `deleted` int DEFAULT 0 COMMENT ''删除标记：0-未删除，1-已删除'',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_douyin_id` (`douyin_id`),
          UNIQUE KEY `uk_star_map_id` (`star_map_id`),
          KEY `idx_batch_id` (`batch_id`),
          KEY `idx_create_time` (`create_time`),
          KEY `idx_deleted` (`deleted`),
          KEY `idx_upload_source` (`upload_source`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=''OPPO用户周表-', year, '年第', week, '周''
    ');
    
    -- 执行SQL语句
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT CONCAT('周表 ', table_name, ' 创建成功') AS result;
END$$

-- 批量创建一年的周表
CREATE PROCEDURE IF NOT EXISTS CreateYearlyUserWeeklyTables(IN year INT)
BEGIN
    DECLARE week_num INT DEFAULT 1;
    
    WHILE week_num <= 52 DO
        CALL CreateWeeklyUserTable(year, week_num);
        SET week_num = week_num + 1;
    END WHILE;
    
    SELECT CONCAT(year, '年的52个用户周表创建完成') AS result;
END$$

DELIMITER ;

-- 使用示例：
-- CALL CreateWeeklyUserTable(2025, 15);  -- 创建2025年第15周的表
-- CALL CreateYearlyUserWeeklyTables(2025);  -- 创建2025年全年的周表

-- 创建批次管理表（如果不存在）
CREATE TABLE IF NOT EXISTS `oppo_user_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(50) NOT NULL COMMENT '批次ID',
  `batch_name` varchar(200) DEFAULT NULL COMMENT '批次名称',
  `upload_source` varchar(50) DEFAULT 'EXCEL' COMMENT '上传来源：EXCEL, API, MANUAL',
  `file_name` varchar(500) DEFAULT NULL COMMENT '上传文件名',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `total_count` int DEFAULT 0 COMMENT '总记录数',
  `success_count` int DEFAULT 0 COMMENT '成功记录数',
  `failed_count` int DEFAULT 0 COMMENT '失败记录数',
  `upload_status` varchar(20) DEFAULT 'PROCESSING' COMMENT '上传状态：PROCESSING, SUCCESS, FAILED, PARTIAL',
  `created_by` varchar(100) DEFAULT 'SYSTEM' COMMENT '创建者',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `remark` text DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_id` (`batch_id`),
  KEY `idx_upload_source` (`upload_source`),
  KEY `idx_upload_status` (`upload_status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='OPPO用户批次管理表';

-- 预创建2025年前20周的表
CALL CreateWeeklyUserTable(2025, 1);
CALL CreateWeeklyUserTable(2025, 2);
CALL CreateWeeklyUserTable(2025, 3);
CALL CreateWeeklyUserTable(2025, 4);
CALL CreateWeeklyUserTable(2025, 5);
CALL CreateWeeklyUserTable(2025, 6);
CALL CreateWeeklyUserTable(2025, 7);
CALL CreateWeeklyUserTable(2025, 8);
CALL CreateWeeklyUserTable(2025, 9);
CALL CreateWeeklyUserTable(2025, 10);
CALL CreateWeeklyUserTable(2025, 11);
CALL CreateWeeklyUserTable(2025, 12);
CALL CreateWeeklyUserTable(2025, 13);
CALL CreateWeeklyUserTable(2025, 14);
CALL CreateWeeklyUserTable(2025, 15);
CALL CreateWeeklyUserTable(2025, 16);
CALL CreateWeeklyUserTable(2025, 17);
CALL CreateWeeklyUserTable(2025, 18);
CALL CreateWeeklyUserTable(2025, 19);
CALL CreateWeeklyUserTable(2025, 20);

-- 查看创建的表
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    CREATE_TIME as '创建时间'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME LIKE 'oppo_user_2025_%'
ORDER BY TABLE_NAME;
