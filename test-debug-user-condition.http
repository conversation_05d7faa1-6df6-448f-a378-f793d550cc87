# 调试用户查询条件

### 1. 调试您的具体查询条件
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": {
    "douyinIds": ["68370542454", "test_user_002"],
    "starMapIds": ["6857515126107406344", "star_test_002"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}

###

### 2. 测试修复后的查询（使用 OR 逻辑）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "douyinIds": ["68370542454", "test_user_002"],
    "starMapIds": ["6857515126107406344", "star_test_002"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}

###

### 3. 只测试抖音ID查询
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": {
    "douyinIds": ["68370542454"]
  }
}

###

### 4. 只测试星图ID查询
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": {
    "starMapIds": ["6857515126107406344"]
  }
}

###

### 5. 只测试抖音链接查询
POST http://localhost:10000/api/oppo/debug-user-condition
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "userCondition": {
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}

###

### 6. 测试数据库中是否有任何数据
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5
}

###

### 7. 查看最新有数据的表信息
GET http://localhost:10000/api/oppo/latest-table

###

### 8. 查看可用的周表列表
GET http://localhost:10000/api/oppo/available-tables
