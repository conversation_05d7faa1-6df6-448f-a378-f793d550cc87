# 测试 Excel 上传 douyin_link 字段功能

### 1. 测试包含 douyin_link 字段的 Excel 上传（去重模式）
POST http://localhost:10000/api/oppo-user/upload-excel
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test_users_with_douyin_link.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

[Excel文件内容 - 包含三列：抖音ID, 星图ID, 抖音链接]
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="token"

eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="batchSize"

50
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###

### 2. 测试包含 douyin_link 字段的 Excel 上传（覆盖模式）
POST http://localhost:10000/api/oppo-user/upload-excel
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test_users_with_douyin_link.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

[Excel文件内容 - 包含三列：抖音ID, 星图ID, 抖音链接]
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="token"

eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overwrite"

true
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="batchSize"

50
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###

### 3. 查询用户数据验证 douyin_link 字段
GET http://localhost:10000/api/oppo-user/douyin/test_douyin_id_001

###

### 4. 根据批次ID查询用户列表，验证 douyin_link 字段
GET http://localhost:10000/api/oppo-user/batch/EXCEL_20250106153000_001/users

###

### 5. 测试用户去重功能（验证 douyin_link 字段不影响去重）
GET http://localhost:10000/api/oppo-user/test-deduplication?douyinId=test_douyin_id_001

###

### 6. 分页查询用户数据，验证 douyin_link 字段
POST http://localhost:10000/api/oppo-user/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5
}

###

### 7. API 方式上传用户数据（包含 douyin_link）
POST http://localhost:10000/api/oppo-user/upload
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "users": [
    {
      "douyinId": "api_test_001",
      "starMapId": "star_api_test_001",
      "douyinLink": "https://www.douyin.com/user/api_test_001"
    },
    {
      "douyinId": "api_test_002",
      "starMapId": "star_api_test_002",
      "douyinLink": "https://www.douyin.com/user/api_test_002"
    }
  ],
  "overwrite": false,
  "batchSize": 100
}

###

### 8. 健康检查
GET http://localhost:10000/api/oppo-user/health

###

### 9. 系统诊断
GET http://localhost:10000/api/oppo-user/diagnose

###

### 10. 查看最近的批次列表
GET http://localhost:10000/api/oppo-user/batches/recent?limit=5
