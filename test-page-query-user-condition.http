# 测试分页查询用户条件优化功能

### 1. 使用新的 userCondition 对象查询（推荐方式）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "douyinIds": ["test_user_001", "test_user_002"],
    "starMapIds": ["star_test_001", "star_test_002"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}

###

### 2. 使用 userCondition 的 userIds 字符串查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "userIds": "test_user_001,test_user_002,star_test_001"
  }
}

###

### 3. 使用 userCondition 的 userIdList 数组查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "userIdList": ["test_user_001", "test_user_002", "star_test_001", "star_test_002"]
  }
}

###

### 4. 使用 userCondition 的混合查询（所有字段）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "userIds": "test_user_001,test_user_002",
    "userIdList": ["test_user_003", "test_user_004"],
    "douyinIds": ["douyin_test_001", "douyin_test_002"],
    "starMapIds": ["star_test_001", "star_test_002"],
    "douyinLinks": [
      "https://www.douyin.com/user/test_user_001",
      "https://www.douyin.com/user/test_user_002"
    ]
  }
}

###

### 5. 只使用抖音链接查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {
    "douyinLinks": [
      "https://www.douyin.com/user/test_user_001",
      "https://www.douyin.com/user/test_user_002",
      "https://v.douyin.com/user/test_user_003"
    ]
  }
}

###

### 6. 向后兼容测试 - 使用原有的字段（不使用 userCondition）
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userIds": "test_user_001,test_user_002",
  "douyinIds": ["douyin_test_001"],
  "starMapIds": ["star_test_001"]
}

###

### 7. 结合批次ID和用户条件查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "batchId": "EXCEL_20250106153000_001",
  "userCondition": {
    "douyinIds": ["test_user_001", "test_user_002"]
  }
}

###

### 8. 结合时间范围和用户条件查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "startUploadTime": "2025-01-01 00:00:00",
  "endUploadTime": "2025-01-31 23:59:59",
  "userCondition": {
    "douyinIds": ["test_user_001"],
    "douyinLinks": ["https://www.douyin.com/user/test_user_001"]
  }
}

###

### 9. 结合关键词搜索和用户条件查询
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "keyword": "test",
  "userCondition": {
    "douyinIds": ["test_user_001", "test_user_002"]
  }
}

###

### 10. 空的 userCondition 对象测试
POST http://localhost:10000/api/oppo/page
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************.y4dQmdztLjL7qNmlbXTce0qoQN7-oA9rTNkC7tdscMNXhwi3yHJz5bK5aiZy1aHXMKt3iC-hZbIvjCF9b2BU_Q",
  "pageNum": 1,
  "pageSize": 5,
  "userCondition": {}
}
